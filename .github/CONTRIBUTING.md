# Contributing to Rocket.Chat

:+1::tada: First off, thanks for taking the time to contribute! :tada::+1:

The following is a set of guidelines for contributing to Rocket.Chat and its
packages, which are hosted in the [Rocket.Chat Organization] on GitHub.

[Rocket.Chat Organization]: https://github.com/RocketChat

__Note:__ If there's a feature you'd like, there's a bug you'd like to fix, or
you'd just like to get involved please raise an issue and start a conversation.
We'll help as much as we can so you can get contributing - although we may not
always be able to respond right away :)

## Coding standards

Most of the coding standards are covered by `.editorconfig` and `.eslintrc`.

We acknowledge all the code does not meet these standards but we are working to
change this over time.

### Syntax check

Before submitting a PR you should get no errors on `eslint`.

To check your files, run:

```sh
yarn lint
```

## Contributor License Agreement

Please review and sign our [CLA].

[CLA]: https://cla-assistant.io/RocketChat/Rocket.Chat
