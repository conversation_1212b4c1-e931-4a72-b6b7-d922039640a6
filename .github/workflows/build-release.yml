permissions:
  contents: read
name: Build release

on:
  push:
    branches:
      - master
      - develop
    tags:
      - '*'

concurrency:
  group: ${{ github.workflow }}-${{ github.head_ref }}
  cancel-in-progress: true

jobs:
  build:
    strategy:
      fail-fast: false
      matrix:
        os:
          - ubuntu-latest
          - macos-latest
          - windows-latest
    runs-on: ${{ matrix.os }}
    steps:
      - name: Disable git core.autocrlf
        run: git config --global core.autocrlf false

      - name: Checkout code
        uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: Setup Node 22.17.1
        uses: actions/setup-node@v4
        with:
          node-version: '22.17.1'

      - name: Setup node_modules cache
        uses: actions/cache@v4
        with:
          path: '**/node_modules'
          key: ${{ runner.os }}-modules-${{ hashFiles('**/yarn.lock') }}

      - name: Install package dependencies
        run: yarn install

      - name: Install build dependencies
        if: runner.os == 'Linux'
        run: |
          sudo apt-get update
          sudo apt-get install -y xz-utils libarchive-tools squashfs-tools

      - name: Lint
        run: yarn lint

      - name: Test
        run: yarn test

      - name: Build app/
        run: yarn build
        env:
          NODE_ENV: production
          BUGSNAG_API_KEY: ${{ secrets.BUGSNAG_API_KEY }}
          SNAPCRAFT_STORE_CREDENTIALS: ${{ secrets.SNAPCRAFT_STORE_CREDENTIALS }}
      - uses: ./workspaces/desktop-release-action
        with:
          mac_csc_link: ${{ secrets.MAC_CSC_LINK }}
          mac_csc_key_password: ${{ secrets.MAC_CSC_KEY_PASSWORD }}
          mac_apple_id: ${{ secrets.APPLEID }}
          mac_apple_id_password: ${{ secrets.APPLEIDPASS }}
          mac_asc_provider: 'S6UPZG7ZR3'
          win_csc_link: ${{ secrets.WIN_CSC_LINK }}
          win_csc_key_password: ${{ secrets.WIN_CSC_KEY_PASSWORD }}
          github_token: ${{ secrets.GH_TOKEN }}
        env:
          SNAPCRAFT_STORE_CREDENTIALS: ${{ secrets.SNAPCRAFT_STORE_CREDENTIALS }}
