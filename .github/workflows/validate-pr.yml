name: Validate pull request

on:
  pull_request:
    branches:
      - master

concurrency:
  group: ${{ github.workflow }}-${{ github.head_ref }}
  cancel-in-progress: true

permissions:
  contents: read
jobs:
  check:
    strategy:
      fail-fast: false
      matrix:
        os:
          - ubuntu-latest
          - macos-latest
          - windows-latest
    runs-on: ${{ matrix.os }}
    steps:
      - name: Disable git core.autocrlf
        run: git config --global core.autocrlf false
      - name: Checkout code
        uses: actions/checkout@v3
      - name: Setup Node 22.17.1
        uses: actions/setup-node@v4
        with:
          node-version: '22.17.1'
      - name: Setup node_modules cache
        uses: actions/cache@v4
        with:
          path: '**/node_modules'
          key: ${{ runner.os }}-modules-${{ hashFiles('**/yarn.lock') }}
      - name: Install package dependencies
        run: yarn install
      - name: Lint
        run: yarn lint
      - name: Test
        run: yarn test
      - name: Build app/
        run: yarn build
        env:
          NODE_ENV: production
      - name: Build uncompressed on dist/
        if: ${{ matrix.os == 'ubuntu-latest' || matrix.os == 'macos-latest' }}
        run: yarn electron-builder --dir
      - name: Build uncompressed on dist/
        if: ${{ matrix.os == 'windows-latest' }}
        run: yarn electron-builder --x64 --ia32 --dir
      - name: Running executable on ${{ runner.os }}
        if: ${{ matrix.os == 'ubuntu-latest' }}
        run: |
          xvfb-run timeout 30 ./dist/linux-unpacked/rocketchat-desktop --no-sandbox || code=$?
          if [[ $code -ne 124 && $code -ne 0 ]]; then
            exit $code;
          fi
      - name: Running executable on ${{ runner.os }}
        if: ${{ matrix.os == 'macos-latest' }}
        run: |
          brew install coreutils
          gtimeout 30 ./dist/mac-arm64/Rocket.Chat.app/Contents/MacOS/Rocket.Chat || code=$?
          if [[ $code -ne 124 && $code -ne 0 ]]; then
            exit $code;
          fi
      - name: Running executable on ${{ runner.os }}
        if: ${{ matrix.os == 'windows-latest' }}
        run: |
          $job = Start-Job -ScriptBlock { .\dist\win-unpacked\Rocket.Chat.exe }
          if (Wait-Job $job -Timeout 30) {
            Write-Host $(Receive-Job -Job $job)
          }
          Remove-Job -force $job
          $job = Start-Job -ScriptBlock { .\dist\win-ia32-unpacked\Rocket.Chat.exe }
          if (Wait-Job $job -Timeout 30) {
            Write-Host $(Receive-Job -Job $job)
          }
          Remove-Job -force $job
