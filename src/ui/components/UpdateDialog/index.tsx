import {
  <PERSON>,
  <PERSON>ton,
  ButtonGroup,
  Ch<PERSON><PERSON>,
  Margins,
} from '@rocket.chat/fuselage';
import { useEffect, useRef } from 'react';
import { useTranslation } from 'react-i18next';
import { useDispatch, useSelector } from 'react-redux';
import type { Dispatch } from 'redux';

import type { RootAction } from '../../../store/actions';
import type { RootState } from '../../../store/rootReducer';
import {
  UPDATE_DIALOG_SKIP_UPDATE_CLICKED,
  UPDATE_DIALOG_REMIND_UPDATE_LATER_CLICKED,
  UPDATE_DIALOG_INSTALL_BUTTON_CLICKED,
  UPDATE_DIALOG_DISMISSED,
} from '../../actions';
import { Dialog } from '../Dialog';

export const UpdateDialog = () => {
  const currentVersion = useSelector(({ appVersion }: RootState) => appVersion);
  const newVersion = useSelector(
    ({ newUpdateVersion }: RootState) => newUpdateVersion
  );
  const openDialog = useSelector(({ openDialog }: RootState) => openDialog);
  const isVisible = openDialog === 'update';

  const dispatch = useDispatch<Dispatch<RootAction>>();

  const { t } = useTranslation();

  const installButtonRef = useRef<HTMLButtonElement>(null);

  useEffect(() => {
    if (!isVisible) {
      return;
    }

    installButtonRef.current?.focus();
  }, [isVisible]);

  const handleSkipButtonClick = (): void => {
    dispatch({ type: UPDATE_DIALOG_SKIP_UPDATE_CLICKED, payload: newVersion });
  };

  const handleRemindLaterButtonClick = (): void => {
    dispatch({ type: UPDATE_DIALOG_REMIND_UPDATE_LATER_CLICKED });
  };

  const handleInstallButtonClick = (): void => {
    dispatch({ type: UPDATE_DIALOG_INSTALL_BUTTON_CLICKED });
  };

  const handleClose = (): void => {
    dispatch({ type: UPDATE_DIALOG_DISMISSED });
  };

  return (
    <Dialog isVisible={isVisible} onClose={handleClose}>
      <Box display='flex' flexDirection='column' alignItems='center'>
        <Margins block='x8'>
          <Box fontScale='h1'>{t('dialog.update.announcement')}</Box>
          <Box>{t('dialog.update.message')}</Box>
        </Margins>

        <Margins block='x32'>
          <Box display='flex' alignItems='center' justifyContent='center'>
            <Margins inline='x16'>
              <Box
                display='flex'
                flexDirection='column'
                alignItems='center'
                color='info'
              >
                <Box>{t('dialog.update.currentVersion')}</Box>
                <Box fontScale='p2'>{currentVersion}</Box>
              </Box>
              <Chevron right size='32' />
              <Box display='flex' flexDirection='column' alignItems='center'>
                <Box>{t('dialog.update.newVersion')}</Box>
                <Box fontScale='p2'>{newVersion}</Box>
              </Box>
            </Margins>
          </Box>
        </Margins>
      </Box>

      <ButtonGroup>
        <Button type='button' onClick={handleSkipButtonClick}>
          {t('dialog.update.skip')}
        </Button>
        <Button type='button' onClick={handleRemindLaterButtonClick}>
          {t('dialog.update.remindLater')}
        </Button>
        <Button
          ref={installButtonRef}
          type='button'
          primary
          onClick={handleInstallButtonClick}
        >
          {t('dialog.update.install')}
        </Button>
      </ButtonGroup>
    </Dialog>
  );
};
