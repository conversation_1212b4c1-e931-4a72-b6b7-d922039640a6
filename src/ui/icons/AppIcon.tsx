import type { ReactNode } from 'react';

type AppIconProps = {
  children?: ReactNode;
  color?: string;
};

const AppIcon = ({ children, color = '#F5455C' }: AppIconProps) => (
  <svg width='100%' viewBox='0 0 512 512' xmlns='http://www.w3.org/2000/svg'>
    <g fill={color} mask={children ? 'url(#cut)' : undefined}>
      <path d='M488.68 179.188C474.795 157.626 455.334 138.538 430.868 122.438C383.599 91.3797 321.49 74.2709 255.981 74.2709C234.094 74.2709 212.531 76.174 191.599 79.9421C178.612 67.4388 163.428 56.1915 147.347 47.3041C87.6824 17.5587 35.1035 28.6347 8.53707 38.1882C-0.19107 41.3283 -2.88399 52.3663 3.57139 59.0271C22.3073 78.3625 53.3046 116.577 45.6842 151.327C16.062 181.567 -8.39233e-05 218.03 -8.39233e-05 255.997C-8.39233e-05 294.687 16.062 331.15 45.6842 361.39C53.3046 396.141 22.3073 434.374 3.57139 453.709C-2.86489 460.351 -0.19107 471.389 8.53707 474.529C35.1035 484.083 87.6824 495.178 147.366 465.432C163.447 456.545 178.631 445.298 191.618 432.794C212.55 436.562 234.113 438.466 256 438.466C321.528 438.466 383.637 421.376 430.887 390.317C455.353 374.217 474.815 355.148 488.699 333.567C504.169 309.55 512 283.687 512 256.739C511.981 229.068 504.15 203.224 488.68 179.188ZM253.307 393.648C224.983 393.648 197.978 389.994 173.359 383.39L155.368 400.689C145.59 410.09 134.131 418.597 122.175 425.296C106.342 433.042 90.7 437.286 75.23 438.561C76.1086 436.981 76.9107 435.383 77.7701 433.784C95.7994 400.67 100.67 370.906 92.3616 344.51C62.8731 321.349 45.1876 291.699 45.1876 259.404C45.1876 185.278 138.371 125.179 253.307 125.179C368.243 125.179 461.445 185.278 461.445 259.404C461.445 333.548 368.263 393.648 253.307 393.648Z' />
      <path d='M153.745 228.535C136.805 228.535 123.073 242.162 123.073 258.966C123.073 275.77 136.805 289.396 153.745 289.396C170.686 289.396 184.418 275.77 184.418 258.966C184.418 242.162 170.686 228.535 153.745 228.535Z' />
      <path d='M252.467 228.535C235.526 228.535 221.794 242.162 221.794 258.966C221.794 275.77 235.526 289.396 252.467 289.396C269.408 289.396 283.14 275.77 283.14 258.966C283.14 242.162 269.408 228.535 252.467 228.535Z' />
      <path d='M351.207 228.535C334.266 228.535 320.534 242.162 320.534 258.966C320.534 275.77 334.266 289.396 351.207 289.396C368.147 289.396 381.879 275.77 381.879 258.966C381.879 242.162 368.147 228.535 351.207 228.535Z' />
    </g>
    {!!children && (
      <>
        <g transform='translate(256 256)'>
          <g transform='translate(128 128)'>
            <g transform='scale(0.4)'>
              <g transform='translate(-256 -256)'>{children}</g>
            </g>
          </g>
        </g>
        <defs>
          <mask id='cut'>
            <rect x='0' y='0' width='512' height='512' fill='white' />
            <g filter='url(#blackout)'>
              <g transform='translate(256 256)'>
                <g transform='translate(128 128)'>
                  <g transform='scale(0.5)'>
                    <g transform='translate(-256 -256)'>{children}</g>
                  </g>
                </g>
              </g>
            </g>
          </mask>
          <filter id='blackout'>
            <feColorMatrix
              type='matrix'
              values='
              0 0 0 0 0
              0 0 0 0 0
              0 0 0 0 0
              0 0 0 1 0
            '
              in='SourceGraphic'
            />
          </filter>
        </defs>
      </>
    )}
  </svg>
);

export default AppIcon;
