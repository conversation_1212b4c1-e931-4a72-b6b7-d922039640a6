const MacOSAppIcon = () => (
  <svg
    width='100%'
    viewBox='0 0 1024 1024'
    fill='none'
    xmlns='http://www.w3.org/2000/svg'
  >
    <g filter='url(#filter0_di)'>
      <path
        fillRule='evenodd'
        clipRule='evenodd'
        d='M924 356.627C924 346.845 924.004 337.062 923.944 327.279C923.895 319.038 923.8 310.799 923.576 302.562C923.092 284.609 922.033 266.502 918.84 248.749C915.602 230.741 910.314 213.98 901.981 197.617C893.789 181.534 883.088 166.817 870.32 154.058C857.555 141.299 842.834 130.605 826.746 122.418C810.366 114.083 793.587 108.797 775.558 105.56C757.803 102.372 739.691 101.315 721.738 100.83C713.495 100.607 705.253 100.513 697.008 100.462C687.22 100.402 677.432 100.407 667.644 100.407L553.997 100H468.997L357.361 100.407C347.554 100.407 337.747 100.402 327.94 100.462C319.678 100.513 311.42 100.607 303.161 100.83C285.167 101.315 267.014 102.373 249.217 105.565C231.164 108.801 214.36 114.085 197.958 122.414C181.835 130.602 167.083 141.297 154.291 154.058C141.501 166.816 130.78 181.53 122.573 197.61C114.217 213.981 108.919 230.752 105.673 248.77C102.477 266.516 101.418 284.617 100.931 302.562C100.709 310.8 100.613 319.039 100.563 327.279C100.503 337.063 100 349.216 100 358.999L100.003 469.089L100 554.998L100.508 667.427C100.508 677.223 100.504 687.019 100.563 696.815C100.613 705.067 100.709 713.317 100.932 721.566C101.418 739.542 102.479 757.675 105.678 775.452C108.923 793.484 114.22 810.269 122.569 826.653C130.777 842.759 141.5 857.495 154.291 870.272C167.082 883.049 181.83 893.757 197.95 901.956C214.362 910.302 231.174 915.595 249.238 918.836C267.027 922.029 285.174 923.088 303.161 923.573C311.42 923.796 319.679 923.891 327.941 923.941C337.748 924.001 347.554 923.997 357.361 923.997L470.006 924H555.217L667.644 923.996C677.432 923.996 687.22 924.001 697.008 923.941C705.253 923.891 713.495 923.796 721.738 923.573C739.698 923.087 757.816 922.027 775.579 918.832C793.597 915.591 810.368 910.3 826.739 901.959C842.831 893.761 857.554 883.051 870.32 870.272C883.086 857.497 893.786 842.763 901.978 826.66C910.316 810.268 915.604 793.475 918.844 775.431C922.034 757.661 923.092 739.535 923.577 721.566C923.8 713.316 923.895 705.066 923.944 696.815C924.005 687.019 924 677.223 924 667.427C924 667.427 923.994 556.983 923.994 554.998V468.999C923.994 467.533 924 356.627 924 356.627Z'
        fill='url(#paint0_linear)'
      />
    </g>
    <path
      d='M767.107 427.378C752.164 404.173 731.22 383.631 704.89 366.304C654.018 332.879 587.176 314.466 516.676 314.466C493.121 314.466 469.915 316.514 447.388 320.569C433.411 307.113 417.07 295.009 399.764 285.444C335.553 253.432 278.967 265.352 250.376 275.634C240.983 279.013 238.085 290.892 245.032 298.061C265.196 318.869 298.555 359.996 290.354 397.394C258.475 429.939 241.188 469.18 241.188 510.04C241.188 551.678 258.475 590.92 290.354 623.465C298.555 660.863 265.196 702.01 245.032 722.818C238.105 729.966 240.983 741.845 250.376 745.225C278.967 755.506 335.553 767.447 399.784 735.435C417.091 725.87 433.431 713.766 447.408 700.31C469.935 704.365 493.141 706.413 516.696 706.413C587.217 706.413 654.06 688.021 704.91 654.596C731.24 637.269 752.185 616.747 767.128 593.521C783.777 567.674 792.204 539.84 792.204 510.839C792.183 481.059 783.756 453.246 767.107 427.378ZM513.798 658.18C483.316 658.18 454.253 654.248 427.758 647.141L408.396 665.758C397.873 675.876 385.54 685.031 372.673 692.24C355.634 700.576 338.8 705.143 322.151 706.515C323.097 704.816 323.96 703.095 324.885 701.375C344.288 665.738 349.529 633.705 340.588 605.298C308.853 580.372 289.82 548.463 289.82 513.706C289.82 433.932 390.103 369.253 513.798 369.253C637.493 369.253 737.797 433.932 737.797 513.706C737.797 593.501 637.513 658.18 513.798 658.18Z'
      fill='white'
    />
    <path
      d='M406.65 480.486C388.418 480.486 373.64 495.15 373.64 513.235C373.64 531.32 388.418 545.984 406.65 545.984C424.881 545.984 439.66 531.32 439.66 513.235C439.66 495.15 424.881 480.486 406.65 480.486Z'
      fill='white'
    />
    <path
      d='M512.894 480.486C494.663 480.486 479.884 495.15 479.884 513.235C479.884 531.32 494.663 545.984 512.894 545.984C531.126 545.984 545.904 531.32 545.904 513.235C545.904 495.15 531.126 480.486 512.894 480.486Z'
      fill='white'
    />
    <path
      d='M619.158 480.486C600.926 480.486 586.148 495.15 586.148 513.235C586.148 531.32 600.926 545.984 619.158 545.984C637.389 545.984 652.168 531.32 652.168 513.235C652.168 495.15 637.389 480.486 619.158 480.486Z'
      fill='white'
    />
    <defs>
      <filter
        id='filter0_di'
        x={90}
        y={97}
        width={844}
        height={847}
        filterUnits='userSpaceOnUse'
        colorInterpolationFilters='sRGB'
      >
        <feFlood floodOpacity={0} result='BackgroundImageFix' />
        <feColorMatrix
          in='SourceAlpha'
          type='matrix'
          values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0'
        />
        <feOffset dy={10} />
        <feGaussianBlur stdDeviation={5} />
        <feColorMatrix
          type='matrix'
          values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.3 0'
        />
        <feBlend
          mode='normal'
          in2='BackgroundImageFix'
          result='effect1_dropShadow'
        />
        <feBlend
          mode='normal'
          in='SourceGraphic'
          in2='effect1_dropShadow'
          result='shape'
        />
        <feColorMatrix
          in='SourceAlpha'
          type='matrix'
          values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0'
          result='hardAlpha'
        />
        <feOffset dy={-3} />
        <feGaussianBlur stdDeviation={2} />
        <feComposite in2='hardAlpha' operator='arithmetic' k2={-1} k3={1} />
        <feColorMatrix
          type='matrix'
          values='0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0'
        />
        <feBlend mode='normal' in2='shape' result='effect2_innerShadow' />
      </filter>
      <linearGradient
        id='paint0_linear'
        x1={512}
        y1={100}
        x2={512}
        y2={924}
        gradientUnits='userSpaceOnUse'
      >
        <stop stopColor='#F5455C' />
        <stop offset={1} stopColor='#DB0C27' />
      </linearGradient>
    </defs>
  </svg>
);
export default MacOSAppIcon;
