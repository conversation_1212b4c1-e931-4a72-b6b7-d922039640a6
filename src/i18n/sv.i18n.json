{"contextMenu": {"cut": "Cu&t", "copy": "&<PERSON><PERSON><PERSON>", "paste": "&Klistra in", "selectAll": "Välj &alla", "undo": "&Ångra", "redo": "&Gör om", "spelling": "Stavning", "spellingLanguages": "Stavningsspråk", "moreSpellingSuggestions": "Fler stavningsförslag", "noSpellingSuggestions": "Inga förslag", "copyLinkAddress": "Ko<PERSON>ra länkadressen", "copyLinkText": "Kopiera länktext", "openLink": "Öppna länken", "saveImageAs": "Spara bilden som...", "copyImage": "<PERSON><PERSON><PERSON> bilden"}, "dialog": {"about": {"title": "Om {{- appName}}", "version": "Version: <1>{{-version}}</1>", "checkUpdates": "<PERSON><PERSON><PERSON> efter upp<PERSON><PERSON><PERSON>", "checkUpdatesOnStart": "<PERSON><PERSON><PERSON> efter uppdateringar vid start", "noUpdatesAvailable": "Inga uppdateringar är tillgängliga.", "copyright": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {{copyright}}", "errorWhenLookingForUpdates": "Ett fel har inträffat vid sökning efter uppdateringar", "updateChannel": {"label": "Uppdatera Kanal", "latest": "Stabil", "beta": "Beta", "alpha": "<PERSON> (experimentell)"}}, "addServer": {"title": "Lägg till server", "message": "Vill du lägga till \"{{- host}}\" till din lista över servrar?", "add": "<PERSON><PERSON><PERSON> till", "cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "addServerError": {"title": "Ogiltigt servernamn", "message": "Servern \"{{- host}}\" kunde inte valideras och lades därför inte till."}, "certificateError": {"title": "Certifikatsfel", "message": "Litar du på certifikat från \"{{- issuerName}}\"? <PERSON>licka bara på \"Ja\" om du verkligen litar på det här certifikatet. Om du är osäker bör du kontakta ditt IT- eller säkerhetsteam innan du klickar på \"Ja\".", "yes": "<PERSON>a", "no": "<PERSON><PERSON>"}, "clearCache": {"announcement": "Tvinga omladdning", "title": "Vill du behålla din inloggningssession?", "message": "Om du tar bort din inloggningssession loggas du ut och ombeds att ange dina inloggningsuppgifter igen.", "keepLoginData": "Behåll inloggningssessionen", "deleteLoginData": "Radera inloggningssessionen", "clearingWait": "Vänligen vänta", "cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "downloadRemoval": {"title": "<PERSON>r du säker?", "message": "Vill du ta bort denna nedl<PERSON>?", "yes": "<PERSON>a", "no": "<PERSON><PERSON>"}, "resetAppData": {"title": "Återställ appdata", "message": "Detta kommer att logga ut dig från alla dina team och återställa appen till dess ursprungliga inställningar. Detta kan inte ångras.", "yes": "<PERSON>a", "cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "clearPermittedScreenCaptureServers": {"title": "<PERSON><PERSON> servrar för sk<PERSON>", "message": "<PERSON>ta kommer att rensa alla behörigheter för skärmdumpsservrar, vilket gör att de ber om tillstånd igen. Detta kan inte ångras.", "yes": "<PERSON>a", "cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "screenshare": {"title": "Dela din skärm", "announcement": "Välj en skärm att dela"}, "update": {"title": "Uppdatering finns tillgänglig", "announcement": "En ny uppdatering finns tillgänglig", "message": "En ny version av Rocket.Chat Desktop App är tillgänglig!", "currentVersion": "Nuvarande version:", "newVersion": "Ny version:", "install": "Installera uppdatering", "remindLater": "<PERSON><PERSON><PERSON><PERSON> mig senare", "skip": "Hoppa över denna version"}, "updateDownloading": {"title": "Nedladdning av uppdatering", "message": "Du kommer att få ett meddelande när uppdateringen är klar att installeras", "ok": "OK"}, "updateInstallLater": {"title": "Installera senare", "message": "Uppdateringen kommer att installeras när du lämnar appen", "ok": "OK"}, "updateReady": {"title": "Upp<PERSON>ring klar för installation", "message": "Upp<PERSON><PERSON><PERSON> har laddats ner", "installNow": "Installera nu", "installLater": "Installera senare"}, "updateSkip": {"title": "<PERSON><PERSON> över uppdatering", "message": "<PERSON>i kommer att meddela dig när nästa uppdatering är tillgänglig.\nOm du ändrar dig kan du kontrollera om det finns uppdateringar i menyn Om.", "ok": "OK"}, "selectClientCertificate": {"announcement": "<PERSON><PERSON><PERSON><PERSON> certifikat", "select": "<PERSON><PERSON><PERSON><PERSON>", "validDates": "<PERSON><PERSON><PERSON> från {{-validStart,}} till {{-validExpiry,}}"}, "openingExternalProtocol": {"title": "Länk med anpassat protokoll", "message": "{{- protocol }} länken kräver en extern applikation.", "detail": "Den begärda länken är {{- url }}. Vill du fortsätta?", "dontAskAgain": "Ö<PERSON>na alltid den här typen av länkar i den tillhörande appen", "yes": "<PERSON>a", "no": "<PERSON><PERSON>"}, "allowVideoCallCaptureScreen": {"title": "Videosamtalet försöker fånga din skärm", "message": "Videosamtalet begär tillstånd att fånga din skärm.", "detail": "Videosamtalet från servern {{- url }} kräver tillstånd för att du ska kunna dela din skärm med andra.", "dontAskAgain": "Tillåt alltid videosamtal från denna server att fånga din skärm", "yes": "<PERSON><PERSON><PERSON>", "no": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "mediaPermission": {"title": "Tillstånd för media krävs", "message": "{{- permissionType}} är för närvarande avaktiverad i dina systeminställningar.", "detail": "<PERSON><PERSON><PERSON> att aktivera videosamtalsfunktioner måste du tillåta åtkomst i systemets sekretessinställningar och sedan starta om programmet.", "openSettings": "Öppna inställningar", "cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "microphone": "Mikrofon", "camera": "<PERSON><PERSON><PERSON>", "both": "Mikrofon och kamera"}, "outlookCalendar": {"title": "Outlook-kalender", "encryptionUnavailableTitle": "Kryptering är inte tillgänglig", "encryptionUnavailable": "Ditt operativsystem stöder inte kryptering.\nDina inloggningsuppgifter lagras i klartext.", "field_required": "Detta fält är obligatoriskt", "remember_credentials": "<PERSON><PERSON> ih<PERSON>g mina inloggningsuppgifter", "cancel": " <PERSON><PERSON><PERSON><PERSON><PERSON>", "submit": "Logga in"}, "supportedVersion": {"title": "Versionen av arbetsytan stöds inte"}}, "downloads": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "notifications": {"downloadFinished": "Nedladdning klar", "downloadInterrupted": "Nedladdning avbruten", "downloadCancelled": "Nedladdning avbruten", "downloadFailed": "<PERSON><PERSON><PERSON><PERSON> misslyckades", "downloadExpired": "Nedladdning har löpt ut", "downloadExpiredMessage": "F<PERSON>rsök ladda ner från källan igen."}, "filters": {"search": "<PERSON>ö<PERSON>", "server": "Server", "mimeType": "<PERSON><PERSON>", "status": "Status", "clear": "Rensa filter", "all": "<PERSON>a", "mimes": {"images": "Bilder", "videos": "<PERSON>r", "audios": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "texts": "Texter", "files": "Filer"}, "statuses": {"paused": "Pausad", "cancelled": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "item": {"cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "copyLink": "<PERSON><PERSON><PERSON> länk", "errored": "Nedladdning avbruten", "pause": "Pausa", "progressSize": "{{receivedBytes, byteSize}} av {{totalBytes, byteSize}} ({{ratio, percentage}})", "remove": "<PERSON> bort från listan", "resume": "<PERSON><PERSON><PERSON><PERSON>", "retry": "Försök igen", "showInFolder": "Visa i mapp"}, "showingResults": "Visar resultat {{first}} - {{last}} av {{count}}"}, "certificatesManager": {"title": "Certifika<PERSON><PERSON>var<PERSON>", "trustedCertificates": "Betrodda certifikat", "notTrustedCertificates": "Inte betrodda certifikat", "item": {"domain": "<PERSON><PERSON><PERSON>", "actions": "Åtgärder", "remove": "<PERSON> bort"}}, "settings": {"title": "Inställningar", "general": "<PERSON><PERSON><PERSON><PERSON>", "certificates": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "options": {"report": {"title": "Rapportera fel till utvecklare", "description": "Rapportera fel anonymt till utvecklarna. Delad information inkluderar appens versionsnummer, typ av operativsystem, server-URL, enhetsspråk och feltyp. Inget innehåll eller användarnamn delas.", "masDescription": "Detta alternativ är inaktiverat när det installeras från Mac App Store, felen kommer att rapporteras via Mac App Stores felrapporteringsprocess."}, "flashFrame": {"title": "Aktivera Flash Frame", "titleDarwin": "Toggle Dock Bounce vid varning", "description": "Blinkar fönstret för att dra till sig användarens uppmärksamhet.", "onLinux": "Vissa Linux-distributioner har inte stöd för den här funktionen.", "descriptionDarwin": "Appikonen studsar i dockan för att dra till sig användarens uppmärksamhet."}, "hardwareAcceleration": {"title": "Hårdvaruacceleration", "description": "Aktiverar användning av hårdvaruacceleration när sådan finns tillgänglig. Programmet laddas om vid ändring."}, "internalVideoChatWindow": {"title": "Öppna videochatt i programfönstret", "description": "Om den är aktiverad öppnas videochatten i programmets fönster i stället för i standardwebbläsaren. För <strong>Google Meet</strong> och <strong><PERSON><PERSON><PERSON></strong> st<PERSON>ds dock inte skärminspelning i Electron-applikationer, så de kommer alltid att öppnas i webbläsaren oavsett denna inställning.", "masDescription": "Detta alternativ är inaktiverat när det installeras från Mac App Store. Av säkerhetsskäl öppnas Video Chat alltid i webbläsaren som standard."}, "minimizeOnClose": {"title": "Minimera vid stängning", "description": "<PERSON><PERSON>r appen stängs kommer den att minimeras, annars kommer applikationen avslutas. Tray Icon måste inaktiveras för att detta ska gälla."}, "menubar": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Visa menyfältet högst upp i fönstret.", "disabledHint": "Kan inte inaktivera menyfältet när sidofältet är inaktiverat. Inställningar skulle bli otillgängliga."}, "sidebar": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Visa sidofältet till vänster i fönstret med serverlistan, nedladdningar och inställningar.", "disabledHint": "Kan inte inaktivera sidofältet när menyfältet är inaktiverat. Inställningar skulle bli otillgängliga."}, "trayIcon": {"title": "<PERSON><PERSON> för <PERSON>", "description": "Visa ikonen i systemfältet. Om ikonen är aktiv kommer appen att döljas i facket när den stängs. Annars avslutas programmet."}, "availableBrowsers": {"title": "Standardwebbläsare", "description": "<PERSON><PERSON><PERSON><PERSON> vilken webbläsare som ska öppna externa länkar från den här appen. Systemstandard använder inställningarna för ditt operativsystem.", "systemDefault": "Systemstandard", "loading": "<PERSON>dd<PERSON> webbläsare...", "current": "<PERSON><PERSON><PERSON><PERSON> fö<PERSON>:"}, "clearPermittedScreenCaptureServers": {"title": "<PERSON><PERSON> be<PERSON>righeter för sk<PERSON>rm<PERSON>", "description": "Ta bort skärmdumpstillstånden som valts för att inte fråga igen vid videosamtal."}, "allowScreenCaptureOnVideoCalls": {"title": "<PERSON><PERSON>t skärmdump under <PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON> skärmdump vid videosamtal. <PERSON>ta kommer att be om tillstånd vid varje videosamtal."}, "ntlmCredentials": {"title": "NTLM-autentiseringsuppgifter", "description": "Tillåt att NTLM-autentiseringsuppgifter används vid anslutning till en server.", "domains": "Domäner som ska använda autentiseringsuppgifterna. Separerade med kommatecken. Använd * för att matcha alla domäner."}, "videoCallWindowPersistence": {"title": "Kom ihåg fönstrets position för <PERSON>", "description": "Spara och återställ position och storlek för videosamtalsfönster mellan sessioner"}}}, "error": {"authNeeded": "Autentise<PERSON> krä<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> <strong>{{- auth}}</strong>", "connectTimeout": "Timeout vid anslutningsförsök", "differentCertificate": "Certifikatet skiljer sig från det tidigare.\n\n {{- detail}}", "noValidServerFound": "Ingen giltig server hittades på URL:en", "offline": "Kontrollera din Internetanslutning!"}, "landing": {"invalidUrl": "<PERSON><PERSON><PERSON><PERSON> webbadress", "validating": "Validerar...", "inputUrl": "Ange URL till din server", "connect": "<PERSON><PERSON><PERSON>"}, "menus": {"about": "Om {{- appName}}", "addNewServer": "Lägg till &ny server", "back": "&Tillbaka", "clearTrustedCertificates": "<PERSON><PERSON> betrodda certifikat", "close": "Stäng", "copy": "&<PERSON><PERSON><PERSON>", "cut": "<PERSON><PERSON><PERSON> ut", "developerMode": "Utvecklarläge", "disableGpu": "Avaktivera GPU", "documentation": "Dokumentation", "downloads": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settings": "Inställningar", "editMenu": "&Editera", "fileMenu": "&Fil", "forward": "&<PERSON>am<PERSON><PERSON>", "helpMenu": "&Hjälp", "hide": "G<PERSON>m {{- appName}}", "hideOthers": "<PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON>", "learnMore": "<PERSON><PERSON><PERSON> mer om detta", "minimize": "Minimera", "openDevTools": "Öppna &utvecklarverktyg", "openDevToolsOnAllWindows": "Öppna &utvecklarverktyg för alla fönster", "paste": "&klistra in", "quit": "Avs<PERSON>a {{- appName}}", "redo": "<PERSON><PERSON><PERSON> om", "reload": "Ladda om", "reloadClearingCache": "Tvinga fram omladdning", "reportIssue": "Rapportera problem", "resetAppData": "Återställ appdata", "resetZoom": "Återställ zoom", "selectAll": "<PERSON><PERSON><PERSON><PERSON> alla", "services": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "showFullScreen": "Fullskärm", "showMenuBar": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "showOnUnreadMessage": "Visa vid olästa meddelanden", "showServerList": "Serverlista", "showTrayIcon": "<PERSON><PERSON> för <PERSON>", "toggleDevTools": "Toggle &DevTools", "openConfigFolder": "Öppna mappen &Konfiguration", "videoCallDevTools": "Öppna videosamtal &DevTools", "videoCallTools": "Verktyg fö<PERSON>", "videoCallDevToolsAutoOpen": "Auto-öppna utvecklarverktyg", "undo": "Å<PERSON><PERSON>", "unhide": "Visa alla", "viewMenu": "&Visa", "windowMenu": "&<PERSON>ö<PERSON>er", "zoomIn": "Zooma in", "zoomOut": "Zooma ut"}, "loadingError": {"title": "<PERSON>n kunde inte laddas", "announcement": "Houston, vi har ett problem", "reload": "Ladda om"}, "videoCall": {"loading": {"initial": "<PERSON><PERSON><PERSON>...", "reloading": "Laddar om videosamtal...", "description": "Vänta medan vi ansluter till videosamtalet"}, "error": {"title": "Videosamtalet kunde inte laddas", "announcement": "Houston, vi har ett problem", "timeout": "Tidsgräns överskriden - videosamtalet kunde inte laddas inom 15 sekunder", "crashed": "Webview kraschade", "maxRetriesReached": "Kunde inte ladda efter flera försök", "reload": "<PERSON>dda om <PERSON><PERSON>"}}, "unsupportedServer": {"title": "{{instanceDomain}} kör en version av Rocket.Chat som inte stöds", "announcement": "En administratör måste uppdatera arbetsytan till en version som stöds för att återaktivera åtkomst från mobil- och skrivbordsappar.", "moreInformation": "<PERSON><PERSON><PERSON> mer om detta"}, "selfxss": {"title": "Sluta!", "description": "Detta är en webbläsarfunktion avsedd för utvecklare. Om någon sa till dig att kopiera och klistra in något här för att aktivera en Rocket.Chat-funk<PERSON> el<PERSON> \"hacka\" n<PERSON><PERSON><PERSON> konto, är det en bluff och kommer att ge dem tillgång till ditt Rocket.Chat-konto.", "moreInfo": "Se https://go.rocket.chat/i/xss för mer information."}, "sidebar": {"addNewServer": "Lägg till ny server", "downloads": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "settings": "Inställningar", "menuTitle": "Anpassa och kontrollera appen", "item": {"reload": "Ladda om", "remove": "<PERSON> bort", "openDevTools": "Öppna utvecklarverktyg", "clearCache": "Rensa cacheminnet", "clearStorageData": "<PERSON><PERSON> lag<PERSON>", "copyCurrentUrl": "Kopiera aktuell URL", "reloadClearingCache": "Tvinga fram omladdning", "serverInfo": "Serverinformation", "supportedVersionsInfo": "Information om versioner som stöds"}, "tooltips": {"unreadMessage": "{{- count}} o<PERSON><PERSON><PERSON> me<PERSON>", "unreadMessages": "{{- count}} o<PERSON><PERSON><PERSON> me<PERSON>", "userNotLoggedIn": "Inte inloggad", "addWorkspace": "<PERSON><PERSON><PERSON> till arbetsyta ({{shortcut}}+N)", "settingsMenu": "Anpassa och kontrollera appen"}}, "touchBar": {"formatting": "Formatering", "selectServer": "Välj server"}, "tray": {"menu": {"show": "Visa", "hide": "G<PERSON><PERSON>", "quit": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "tooltip": {"noUnreadMessage": "{{- appName}}: inget oläst meddelande", "unreadMention": "{{- appName}}: du har ett oläst meddelande/direktmeddelande", "unreadMention_plural": "{{- appName}}: du har {{- count}} olästa omnämnanden/direkt meddelanden", "unreadMessage": "{{- appName}}: du har o<PERSON><PERSON><PERSON> medd<PERSON>"}, "balloon": {"stillRunning": {"title": "{{- appName}} är fort<PERSON>ande ig<PERSON>ng", "content": "{{- appName }} är inställd på att fortsätta köras i systemfältet/notifikationsområdet."}}}, "taskbar": {"unreadMessage": "<PERSON><PERSON><PERSON><PERSON> medd<PERSON>", "unreadMention": "<PERSON><PERSON>ästa omnämnanden", "noUnreadMessage": "Inga olästa meddelanden"}, "screenSharing": {"permissionDenied": "Tillstånd för sk<PERSON>ning har nekats", "permissionRequired": "Tillstånd för skärminspelning krävs för att dela din skärm.", "permissionInstructions": "Aktivera det i dina systeminställningar och försök igen.", "title": "Dela din skärm", "entireScreen": "<PERSON><PERSON>", "applicationWindow": "Applikationsfönster", "noScreensFound": "Inga skärmar hittades", "noWindowsFound": "Inga fönster hittades", "cancel": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "share": "Dela"}}