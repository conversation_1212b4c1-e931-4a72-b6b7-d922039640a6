{"contextMenu": {"cut": "&Ausschneiden", "copy": "&<PERSON><PERSON><PERSON>", "paste": "&Einfügen", "selectAll": "Alle auswählen", "undo": "Wider&rufen", "redo": "Wieder&holen", "spelling": "Rechtschreibung", "spellingLanguages": "Sprachen für die Rechtschreibprüfung", "moreSpellingSuggestions": "Mehr Rechtschreibvorschläge", "noSpellingSuggestions": "<PERSON><PERSON>", "copyLinkAddress": "<PERSON>", "copyLinkText": "Linktext kopieren", "openLink": "<PERSON>", "saveImageAs": "Bild speichern als...", "copyImage": "<PERSON><PERSON><PERSON> das Bild"}, "dialog": {"about": {"title": "Über {{- appName}}", "version": "Version: <1>{{-version}}</1>", "checkUpdates": "Auf Aktualisierungen prüfen", "checkUpdatesOnStart": "<PERSON><PERSON> Start auf Aktualisierungen prüfen", "noUpdatesAvailable": "Es sind keine Aktualisierungen verfügbar.", "copyright": "Copyright {{copyright}}", "errorWhenLookingForUpdates": "Bei der Suche nach Updates ist ein Fehler aufgetreten", "updateChannel": {"label": "Update-Kanal", "latest": "Stabil", "beta": "Beta", "alpha": "Alpha (Experimentell)"}}, "addServer": {"title": "Server hinz<PERSON><PERSON><PERSON>", "message": "<PERSON><PERSON><PERSON><PERSON> Si<PERSON> \"{{- host}}\" zu Ihrer Serverliste hinzufügen?", "add": "Hinzufügen", "cancel": "Abbrechen"}, "addServerError": {"title": "Ungültiger Host", "message": "Der Host \"{{- host}}\" konnte nicht validiert werden, er wurde also nicht hinzugefügt."}, "certificateError": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "message": "Vertrauen Sie dem Zertifikat von \"{{- issuerName}}\"?", "yes": "<PERSON>a", "no": "<PERSON><PERSON>"}, "downloadRemoval": {"title": "Bist du dir sicher?", "message": "Diesen Download entfernen?", "yes": "<PERSON>a", "no": "<PERSON><PERSON>"}, "resetAppData": {"title": "Bist du dir sicher?", "yes": "<PERSON>a", "cancel": "Abbrechen"}, "screenshare": {"title": "Bildschirm teilen", "announcement": "Wählen Sie den zu teilenden Bildschirm aus"}, "update": {"title": "Aktualisierung verfügbar", "announcement": "Neue Aktualisierung verfügbar", "message": "Eine neue Version der Rocket.Chat Desktop App ist verfügbar!", "currentVersion": "Aktuelle Version:", "newVersion": "Neue Version:", "install": "Update installieren", "remindLater": "Später erinnern", "skip": "Diese Version überspringen"}, "updateDownloading": {"title": "Aktualisierung wird heruntergeladen", "message": "Sie werden benachrichtigt, wenn die Aktualisierung bereit zur Installation ist", "ok": "OK"}, "updateInstallLater": {"title": "Wird später installiert", "message": "Die Aktualisierung wird durchgeführt, wenn Sie die App beenden", "ok": "OK"}, "updateReady": {"title": "Aktualisierung bereit zur Installation", "message": "Aktualisierung wurde heruntergeladen", "installNow": "Jetzt installieren", "installLater": "Später installieren"}, "updateSkip": {"title": "Aktualisierung überspringen", "message": "Wir werden Sie benachrichtigen, wenn die nächste Aktualisierung verfügbar ist\n Wenn Sie Ihre Meinung ändern, können Sie im Über Menü nach Aktualisierungen suchen.", "ok": "OK"}, "selectClientCertificate": {"announcement": "Wählen Sie Zertifikat aus", "select": "Auswählen", "validDates": "<PERSON><PERSON><PERSON><PERSON> ab {{-validStart,}} zu {{-validExpiry,}}"}, "openingExternalProtocol": {"title": "Link mit benutzerdefiniertem Protokoll", "message": "{{- protocol }} Link er<PERSON><PERSON> eine externe Anwendung.", "detail": "Der angeforderte Link ist {{- url }} . Möchtest du weiter machen?", "dontAskAgain": "Öffnen Sie solche Links immer in der zugehörigen App", "yes": "<PERSON>a", "no": "<PERSON><PERSON>"}, "allowVideoCallCaptureScreen": {"title": "Der Videoanruf vers<PERSON>t, Ihren Bildschirm aufnehmen", "message": "Der Videoanruf fragt nach der Erlaubnis, Ihren Bildschirm aufzunehmen.", "detail": "Der Videoanrufserver {{- url }} benötigt die Erlaubnis, deinen Bildschirm mit anderen zu teilen.", "dontAskAgain": "<PERSON><PERSON> immer zu, dass Videoanrufe von diesem Server Ihren Bildschirm erfassen", "yes": "Erlauben", "no": "Absagen"}, "mediaPermission": {"title": "Medienberechtigung Erforderlich", "message": "Der Zugriff auf {{- permissionType}} ist derzeit in Ihren Systemeinstellungen deaktiviert.", "detail": "Um Videoanruf-Funktionen zu aktivieren, erlauben Si<PERSON> bitte den Zugriff in den Datenschutzeinstellungen Ihres Systems und starten Sie dann die Anwendung neu.", "openSettings": "Einstellungen Öffnen", "cancel": "Abbrechen", "microphone": "Mikrofon", "camera": "<PERSON><PERSON><PERSON>", "both": "Mikrofon und Kamera"}}, "downloads": {"title": "Downloads", "notifications": {"downloadFinished": "<PERSON><PERSON><PERSON>", "downloadInterrupted": "Herunterladen unterbrochen", "downloadCancelled": "Herunterladen abgebrochen", "downloadFailed": "Herunterladen fehlgeschlagen", "downloadExpired": "Herunterladen abgelaufen", "downloadExpiredMessage": "<PERSON>te versuchen Si<PERSON> er<PERSON>, von der Quelle herunterzuladen."}, "filters": {"search": "<PERSON><PERSON>", "server": "Server", "mimeType": "<PERSON><PERSON>", "status": "Status", "clear": "<PERSON><PERSON>", "all": "Alle", "mimes": {"images": "Bilder", "videos": "Videos", "audios": "Audios", "texts": "Texte", "files": "<PERSON><PERSON>"}, "statuses": {"paused": "Ang<PERSON><PERSON><PERSON>", "cancelled": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "item": {"cancel": "Absagen", "copyLink": "<PERSON>", "errored": "Herunterladen abgebrochen", "pause": "Pause", "progressSize": "{{receivedBytes, byteSize}} von {{totalBytes, byteSize}} ({{ratio, percentage}})", "remove": "Aus Liste entfernen", "resume": "Fortsetzen", "retry": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "showInFolder": "<PERSON><PERSON> Ordn<PERSON> anzeigen"}, "showingResults": "Ergebnisse anzeigen {{first}} - {{last}} von {{count}}"}, "certificatesManager": {"title": "Zertifikatsmanager", "trustedCertificates": "Vertrauenswürdige Zertifikate", "notTrustedCertificates": "Nicht vertrauenswürdige Zertifikate", "item": {"domain": "Domain", "actions": "Aktionen", "remove": "Entfernen"}}, "settings": {"title": "Einstellungen", "general": "Allgemein", "certificates": "Zertifikate", "options": {"report": {"title": "<PERSON><PERSON> an Entwickler melden", "description": "Melden Sie Fehler anonym an die Entwickler. Zu den freigegebenen Informationen gehören App-Versionsnummer, Betriebssystemtyp, Server-URL, Gerätesprache und Fehlertyp. Es werden keine Inhalte oder Benutzernamen geteilt.", "masDescription": "Diese Option ist bei der Installation aus dem Mac App Store deaktiviert, die Fehler werden über den Fehlerberichtsprozess des Mac App Store gemeldet."}, "flashFrame": {"title": "Flashframe aktivieren", "titleDarwin": "Schalten Sie Dock Bounce bei Alarm um", "description": "L<PERSON><PERSON><PERSON> das Fenster blinken, um die Aufmerksamkeit des Benutzers zu erregen.", "onLinux": "Einige Linux-Distributionen unterstützen diese Funktion nicht.", "descriptionDarwin": "Lässt das App-Symbol im Dock hüpfen, um die Aufmerksamkeit des Benutzers zu erregen."}, "hardwareAcceleration": {"title": "Hardware-Beschleunigung", "description": "Aktiviert die Verwendung der Hardwarebeschleunigung, sofern verfügbar. Die Anwendung wird bei Änderung neu geladen."}, "internalVideoChatWindow": {"title": "Öffnen Sie den Video-Chat mit dem Anwendungsfenster", "description": "<PERSON><PERSON> diese Option aktiviert ist, wird der Video-Chat im Anwendungsfenster anstelle des Standardbrowsers geöffnet. Allerdings wird für <strong>Google Meet</strong> und <strong>Jitsi</strong> die Bildschirmaufzeichnung in Electron-Anwendungen nicht unterstützt, daher werden sie unabhängig von dieser Einstellung immer im Browser geöffnet.", "masDescription": "Diese Option ist deaktiviert, wenn sie aus dem Mac App Store installiert wird. Aus Sicherheitsgründen wird der Video-Chat standardmäßig über den Browser geöffnet."}, "minimizeOnClose": {"title": "<PERSON>im <PERSON> minimieren", "description": "<PERSON><PERSON> wird die App minimiert, andernfalls wird die Anwendung beendet. Das Taskleistensymbol muss deaktiviert werden, damit dies wirksam wird."}, "menubar": {"title": "Menüleiste", "description": "Menüleiste oben im Fenster anzeigen.", "disabledHint": "Die Menüleiste kann nicht deaktiviert werden, wenn die Seitenleiste bereits deaktiviert ist. Die Einstellungen wären sonst nicht erreichbar."}, "sidebar": {"title": "Seitenleiste", "description": "Seitenleiste auf der linken Seite des Fensters mit Serverliste, Downloads und Einstellungen anzeigen.", "disabledHint": "Die Seitenleiste kann nicht deaktiviert werden, wenn die Menüleiste bereits deaktiviert ist. Die Einstellungen wären sonst nicht erreichbar."}, "trayIcon": {"title": "Taskleistensymbol", "description": "Zeigt ein Symbol in der Systemleiste an. Wenn das Taskleistensymbol aktiv ist, wird die App beim <PERSON>ßen in der Taskleiste minimiert. Andernfalls wird die Anwendung beendet."}, "availableBrowsers": {"title": "Standard-Browser", "description": "<PERSON><PERSON><PERSON><PERSON> Si<PERSON> aus, welcher Browser externe Links aus dieser App ö<PERSON>nen soll. Systemstandard verwendet die Einstellungen Ihres Betriebssystems.", "systemDefault": "Systemstandard", "loading": "Browser werden geladen...", "current": "Aktuell verwendet:"}, "clearPermittedScreenCaptureServers": {"title": "Erlaubte Bildschirmaufnahmeserver löschen", "description": "Löschen Sie Server, die Bildschirme von dieser App erfassen dürfen"}, "ntlmCredentials": {"title": "NTLM-Anmeldeinformationen", "description": "Erlauben Sie die Verwendung von NTLM-Anmeldeinformationen bei der Verbindung mit einem Server.", "domains": "<PERSON><PERSON><PERSON>, die die Anmeldeinformationen verwenden werden. Durch Komma getrennt. Verwenden Sie *, um alle Domänen abzugleichen."}, "videoCallWindowPersistence": {"title": "Videoanruf-Fensterposition speichern", "description": "Position und Größe der Videoanruf-Fenster zwischen Sitzungen speichern und wiederherstellen"}}}, "error": {"authNeeded": "Auth <PERSON><PERSON><PERSON><PERSON>, pro<PERSON><PERSON> <strong>{{- auth}}</strong>", "connectTimeout": "Zeitüberschreitung beim Verbindungsaufbau", "differentCertificate": "Zertifikat unterscheidet sich vom Vorigen.\n\n {{- detail}}", "noValidServerFound": "<PERSON><PERSON> g<PERSON> Server unter dieser URL gefunden", "offline": "Überprüfen Sie Ihre Internetverbindung!"}, "landing": {"invalidUrl": "Ungültige url", "validating": "Validieren...", "inputUrl": "Server URL eingeben", "connect": "Verbinden"}, "menus": {"about": "Über {{- appName}}", "addNewServer": "&Neuen server hinzufügen", "back": "&<PERSON> Rücken", "clearTrustedCertificates": "Vertraute zertifikate löschen", "close": "Schließen", "copy": "&<PERSON><PERSON><PERSON>", "cut": "&Ausschneiden", "developerMode": "Entwicklermodus", "disableGpu": "GPU deaktivieren", "documentation": "Dokumentation", "downloads": "Downloads", "settings": "Einstellungen", "editMenu": "&Bearbeiten", "fileMenu": "&<PERSON>i", "forward": "&<PERSON><PERSON> vorne", "helpMenu": "&Hilf<PERSON>", "hide": "Ausblenden {{- appName}}", "learnMore": "<PERSON><PERSON> er<PERSON>", "minimize": "Minimieren", "openDevTools": "&DevTools ö<PERSON>nen", "openDevToolsOnAllWindows": "Öffnen Sie &DevTools in allen Fenstern", "paste": "&Einfügen", "quit": "{{- appName}} &beenden", "redo": "Wieder&holen", "reload": "&Neu laden", "reportIssue": "Problem melden", "resetAppData": "Appdaten löschen", "resetZoom": "Originalgröße", "selectAll": "Alle auswählen", "services": "Dienstleistungen", "showFullScreen": "Vollbildmodus", "showMenuBar": "<PERSON><PERSON><PERSON><PERSON>", "showOnUnreadMessage": "Bei ungelesenen Nachrichten anzeigen", "showServerList": "Server liste", "showTrayIcon": "Symbole in menüleiste", "toggleDevTools": "&DevTools ein-/ausblenden", "undo": "Wider&rufen", "unhide": "<PERSON><PERSON><PERSON> alles", "viewMenu": "Darstellung", "windowMenu": "&Fenster", "zoomIn": "Vergrößern", "zoomOut": "Verkleinern"}, "loadingError": {"title": "Server konnte nicht geladen werden", "announcement": "Houston, we have a problem", "reload": "Neu laden"}, "videoCall": {"loading": {"initial": "<PERSON><PERSON><PERSON><PERSON> wird geladen...", "reloading": "Video<PERSON><PERSON><PERSON> wird neu geladen...", "description": "<PERSON>te warten <PERSON>, während wir eine Verbindung zum Videoanruf herstellen"}, "error": {"title": "Videoanruf konnte nicht geladen werden", "announcement": "Houston, wir haben ein <PERSON>", "timeout": "Ladezeitüberschreitung - Videoanruf konnte nicht innerhalb von 15 Sekunden geladen werden", "crashed": "Webview ist abgestürzt", "maxRetriesReached": "Laden nach mehreren Versuchen fehlgeschlagen", "reload": "<PERSON><PERSON><PERSON><PERSON> neu laden"}}, "selfxss": {"title": "Halt!", "description": "Dies ist eine Browserfunktion, die für Entwickler gedacht ist. Wenn Ihnen jemand gesagt hat, dass Si<PERSON> hier etwas kopieren und einfügen sollen, um eine Rocket.Chat-Funktion zu aktivieren, oder \"hack\" das Konto einer anderen Person, ist dies ein Betrug und verschafft dieser Person Zugriff auf Ihr Rocket.Chat-Konto.", "moreInfo": "Weitere Informationen finden Si<PERSON> unter https://go.rocket.chat/i/xss."}, "sidebar": {"addNewServer": "Neuen Server hinzufügen", "downloads": "Downloads", "settings": "Einstellungen", "item": {"reload": "Server neu laden", "remove": "Server entfernen", "openDevTools": "DevT<PERSON><PERSON>", "clearCache": "<PERSON><PERSON> le<PERSON>n", "clearStorageData": "Speicherdaten löschen"}}, "touchBar": {"formatting": "Formatierung", "selectServer": "Server ausw<PERSON>hlen"}, "tray": {"menu": {"show": "Anzeigen", "hide": "Ausblenden", "quit": "<PERSON>den"}, "tooltip": {"noUnreadMessage": "{{- appName}}: keine un<PERSON><PERSON><PERSON>", "unreadMention": "{{- appName}}: Sie haben eine ungelesene Erwähnung/Direktnachricht", "unreadMention_plural": "{{- appName}}: you have {{- count}} ungelesene Erwähnungen/Direktnachrichten", "unreadMessage": "{{- appName}}: <PERSON>e haben ungelesene Nachrichten"}, "balloon": {"stillRunning": {"title": "{{- appName}} l<PERSON>uft noch", "content": "{{- appName }} ist so e<PERSON><PERSON>t, dass es in der Taskleiste/im Benachrichtigungsbereich ausgeführt wird."}}}, "taskbar": {"unreadMessage": "Ungelesene Nachrichten", "unreadMention": "Ungelesene Erwähnungen", "noUnreadMessage": "<PERSON><PERSON> ungelesenen Nachrichten"}, "screenSharing": {"permissionDenied": "Bildschirmaufnahme-Berechtigung verweigert", "permissionRequired": "Die Bildschirmaufnahme-Berechtigung ist erforderlich, um Ihren Bildschirm zu teilen.", "permissionInstructions": "Bitte aktivieren Sie diese in den Systemeinstellungen und versuchen Sie es erneut.", "title": "Bildschirm teilen", "entireScreen": "<PERSON>hr gesamter Bildschirm", "applicationWindow": "Anwendungsfenster", "noScreensFound": "<PERSON><PERSON> Bildschirme gefunden", "noWindowsFound": "<PERSON><PERSON> gefunden", "cancel": "Abbrechen", "share": "Teilen"}}