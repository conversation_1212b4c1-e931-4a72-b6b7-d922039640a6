{"contextMenu": {"cut": "Recor&tar", "copy": "&Copiar", "paste": "Co&lar", "selectAll": "&Selecionar tudo", "undo": "&Desfazer", "redo": "&Refazer", "spelling": "Verificação ortográfica", "spellingLanguages": "Idiomas", "moreSpellingSuggestions": "Mais sugestões de grafia", "noSpellingSuggestions": "Se<PERSON> sugestõ<PERSON>", "copyLinkAddress": "Copiar endereço do link", "copyLinkText": "Copiar texto do link", "openLink": "Abrir link", "saveImageAs": "<PERSON><PERSON> imagem como...", "copyImage": "Copiar imagem"}, "dialog": {"about": {"title": "<PERSON><PERSON> {{- appName}}", "version": "Versão: <1>{{-version}}</1>", "checkUpdates": "Verificar Atualizações", "checkUpdatesOnStart": "Verificar Atualizações ao Abrir", "noUpdatesAvailable": "Não há atualizações disponíveis.", "copyright": "{{copyright}} Todos os direitos reservados.", "errorWhenLookingForUpdates": "Ocorreu um erro ao procurar por atualizações", "updateChannel": {"label": "Canal de atualização", "latest": "<PERSON><PERSON><PERSON><PERSON>", "beta": "Beta", "alpha": "Alpha (Experimental)"}}, "addServer": {"title": "<PERSON><PERSON><PERSON><PERSON>", "message": "Você quer adicionar \"{{- host}}\" a sua lista de servidores?", "add": "<PERSON><PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>"}, "addServerError": {"title": "Host inv<PERSON>lido", "message": "O host \"{{- host}}\" não pôde ser validado, portanto não foi adicionado."}, "certificateError": {"title": "Erro de Certificado", "message": "Você confia no certificado de \"{{- issuerName}}\"? Clique em 'Sim' apenas se você realmente confiar neste certificado. Se estiver em dúvida, entre em contato com sua equipe de TI ou segurança antes de clicar em 'Sim'.", "yes": "<PERSON>m", "no": "Não"}, "clearCache": {"announcement": "<PERSON><PERSON><PERSON>", "title": "Deseja manter a sessão de login?", "message": "Ao excluir sua sessão de login, você será desconectado e precisará informar suas credenciais novamente.", "keepLoginData": "Manter sess<PERSON> de login", "deleteLoginData": "Excluir sessão de login", "clearingWait": "<PERSON><PERSON>e, por favor", "cancel": "<PERSON><PERSON><PERSON>"}, "downloadRemoval": {"title": "Você tem certeza?", "message": "Remover este download?", "yes": "<PERSON>m", "no": "Não"}, "resetAppData": {"title": "Limpar dados do aplicativo", "message": "Isto vai deslogá-lo de todos os times e resetar o aplicativo para suas configurações originais. Isto não pode ser desfeito.", "yes": "<PERSON>m", "cancel": "<PERSON><PERSON><PERSON>"}, "clearPermittedScreenCaptureServers": {"title": "Limpar permissões de servidores com captura de tela", "message": "<PERSON><PERSON> vai limpar todas as permissões de servidores com captura de tela. Isto não pode ser desfeito.", "yes": "<PERSON>m", "cancel": "<PERSON><PERSON><PERSON>"}, "screenshare": {"title": "Compartilhe sua tela", "announcement": "Selecione uma tela para compartilhar"}, "update": {"title": "Atualização Disponível", "announcement": "Nova versão está disponível", "message": "Uma nova versão do aplicativo desktop Rocket.Chat está disponível!", "currentVersion": "<PERSON>ersão Atual:", "newVersion": "Nova Versão:", "install": "Instalar Atualização", "remindLater": "<PERSON><PERSON><PERSON>", "skip": "<PERSON>ular Versão"}, "updateDownloading": {"title": "<PERSON><PERSON><PERSON>", "message": "Você será notificado quando a atualização estiver pronta para instalação", "ok": "OK"}, "updateInstallLater": {"title": "<PERSON><PERSON><PERSON><PERSON>", "message": "Atualização será instalada quando você sair do aplicativo", "ok": "OK"}, "updateReady": {"title": "Atualização Pronta para Instalar", "message": "Atualização foi baixada", "installNow": "<PERSON><PERSON>ar <PERSON>", "installLater": "<PERSON><PERSON><PERSON>"}, "updateSkip": {"title": "Pular Atualização", "message": "Nós iremos lembrá-lo quando a próxima atualização estiver disponível.\nSe você mudar de ideia, pode verificar as atualizações no menu Sobre.", "ok": "OK"}, "selectClientCertificate": {"announcement": "Selecione o Certificado", "select": "Selecionar", "validDates": "<PERSON><PERSON><PERSON><PERSON> de {{-validStart,}} a {{-validExpiry,}}"}, "openingExternalProtocol": {"title": "Link com protocolo externo", "message": "O link {{- protocol }} requer uma aplicação externa.", "detail": "O link solicitado é {{- url }} . Você quer continuar?", "dontAskAgain": "Abrir sempre estes tipos de links no aplicativo associado", "yes": "<PERSON>m", "no": "Não"}, "allowVideoCallCaptureScreen": {"title": "Permitir <PERSON> de Tela pela Video Chamada", "message": "A Video chamada está solicitando permissão para capturar sua tela.", "detail": "A video chamada do servidor {{- url }} requer permissão para compartilhar sua tela com outros.", "dontAskAgain": "Sempre permitir a captura de tela por video chamadas neste servidor", "yes": "<PERSON><PERSON><PERSON>", "no": "<PERSON><PERSON><PERSON>"}, "mediaPermission": {"title": "Permissão de Mídia <PERSON>ec<PERSON>", "message": "O acesso ao {{- permissionType}} está atualmente desabilitado nas configurações do seu sistema.", "detail": "Para ativar os recursos de videochamada, permita o acesso nas configurações de privacidade do seu sistema e reinicie a aplicação.", "openSettings": "<PERSON><PERSON><PERSON>figu<PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "microphone": "Microfone", "camera": "Câmera", "both": "Microfone e Câmera"}, "supportedVersion": {"title": "Versão de workspace não suportada"}}, "downloads": {"title": "Downloads", "notifications": {"downloadFinished": "Download finalizado"}, "filters": {"search": "Buscar", "server": "<PERSON><PERSON><PERSON>", "mimeType": "Tipo", "status": "Status", "clear": "Limpar filtros", "all": "Todos", "mimes": {"images": "Imagens", "videos": "Vídeos", "audios": "<PERSON><PERSON><PERSON>", "texts": "Textos", "files": "<PERSON>r<PERSON><PERSON>"}, "statuses": {"paused": "<PERSON><PERSON><PERSON>", "cancelled": "Cancelado"}}, "item": {"cancel": "<PERSON><PERSON><PERSON>", "copyLink": "Copiar link", "errored": "Download cancelado", "pause": "Pause", "progressSize": "{{receivedBytes, byteSize}} de {{totalBytes, byteSize}} ({{ratio, percentage}})", "remove": "Remover da lista", "resume": "<PERSON><PERSON><PERSON><PERSON>", "retry": "Tentar novamente", "showInFolder": "Mostrar na Pasta"}, "showingResults": "Mostrando resultados {{first}} - {{last}} de {{count}}"}, "certificatesManager": {"title": "Gerenciador de certificados", "trustedCertificates": "Certificados confiáveis", "notTrustedCertificates": "Certificados não confiáveis", "item": {"domain": "<PERSON><PERSON><PERSON>", "actions": "Ações", "remove": "Remover"}}, "settings": {"title": "Configurações", "general": "G<PERSON>", "certificates": "Certificados", "options": {"report": {"title": "Relatar erros aos desenvolvedores", "description": "Reporte os erros anonimamente aos desenvolvedores. As informações compartilhadas incluem número de versão do aplicativo, tipo de sistema operacional, URL do servidor, idioma do dispositivo e tipo de erro. Nenhum conteúdo ou nome de usuário é compartilhado.", "masDescription": "Esta opção esta desativada quando for instalado através da Mac App Store, os erros serão reportados através do processo de relatórios de erros da Mac Apple Store."}, "flashFrame": {"title": "<PERSON><PERSON><PERSON> janela", "titleDarwin": "Ícone deve saltar na dock", "description": "Pisca a janela para atrair a atenção do usuário.", "onLinux": "Algumas distribuições Linux não possuem suporte a esta funcionalidade.", "descriptionDarwin": "Faz o ícone da aplicação saltar na dock quando recebe notificação."}, "hardwareAcceleration": {"title": "Aceleração de hardware", "description": "Ativa aceleração de hardware quando disponível. O aplicativo será reiniciado ao ser alterado."}, "internalVideoChatWindow": {"title": "Abrir chat em video em uma janela da aplicação", "description": "Se ativado, o Chat de Vídeo será aberto na janela do aplicativo em vez do navegador padrão. No entanto, para <strong>Google Meet</strong> e <strong><PERSON><PERSON><PERSON></strong>, a gravação de tela não é suportada em aplicativos Electron, então eles sempre serão abertos no navegador independentemente desta configuração.", "masDescription": "Esta opção esta desativada quando for instalado através da Mac App Store, por motivos de segurança o chat em video sera aberto usando o navegador por padrão."}, "minimizeOnClose": {"title": "<PERSON><PERSON><PERSON> ao fechar", "description": "Quando fechado o aplicativo será minimizado para a barra de tarefas, senão será fechado. Ícone da bandeja precisa estar desativado para que isto funcione."}, "menubar": {"title": "Barra de menus", "description": "Mostra a barra de menus no topo da aplicação", "disabledHint": "Não é possível desativar a barra de menus quando a barra lateral está desativada. As configurações se tornariam inacessíveis."}, "sidebar": {"title": "Barra lateral", "description": "Mostra a barra na lateral da janela com a lista de servidores, downloads e configurações.", "disabledHint": "Não é possível desativar a barra lateral quando a barra de menus está desativada. As configurações se tornariam inacessíveis."}, "trayIcon": {"title": "Ícone da bandeja", "description": "Mostra um ícone na bandeja do sistema para acessar rapidamente a aplicação. Se o ícone da bandeja estiver ativado, a aplicação será minimizada para a barra de tarefas ao ser fechada. Por outro lado se o ícone da bandeja estiver desativado, a aplicação será finalizada ao ser fechada."}, "availableBrowsers": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "Escolha qual navegador abrirá os links externos deste aplicativo. Sistema Padrão usa as configurações do seu sistema operacional.", "systemDefault": "Sistema <PERSON>drão", "loading": "Carregando <PERSON>...", "current": "<PERSON>ando atualmente:"}, "clearPermittedScreenCaptureServers": {"title": "Limpar Permissões de Captura de Tela", "description": "<PERSON><PERSON> as permissões de captura de tela que foram selecionadas para não perguntar novamente em chamadas de vídeo."}, "allowScreenCaptureOnVideoCalls": {"title": "Permitir Captura de Tela em Chamadas de Vídeo", "description": "Permitir captura de tela em chamadas de vídeo. Isso solicitará permissão em cada chamada de vídeo."}, "ntlmCredentials": {"title": "Credenciais NTLM", "description": "Per<PERSON><PERSON> que as credenciais NTLM sejam usadas ao conectar-se a um servidor.", "domains": "Domínios que usarão as credenciais. Separados por vírgula. Use * para corresponder a todos os domínios."}, "videoCallWindowPersistence": {"title": "Lembrar posição da janela de videochamada", "description": "Salvar e restaurar a posição e o tamanho das janelas de videochamada entre as sessões"}}}, "error": {"authNeeded": "Autenticação necessária, tente <strong>{{- auth}}</strong>", "connectTimeout": "Tempo esgotado ao conectar", "differentCertificate": "Certificado é diferente do antigo.\n\n {{- detail}}", "noValidServerFound": "Nenhum servidor válido encontrado neste endereço", "offline": "Verifique sua conexão com a Internet!"}, "landing": {"invalidUrl": "Endereço inválido", "validating": "Validando...", "inputUrl": "Insira o endereço do seu servidor", "connect": "Conectar"}, "menus": {"about": "<PERSON><PERSON> {{- appName}}", "addNewServer": "Adicionar &novo servidor", "back": "&Voltar", "clearTrustedCertificates": "Limpar certificados confiáveis", "close": "<PERSON><PERSON><PERSON>", "copy": "&Copiar", "cut": "Recor&tar", "developerMode": "<PERSON><PERSON>", "disableGpu": "Desabilitar GPU", "documentation": "Documentação", "downloads": "Downloads", "settings": "Configurações", "editMenu": "&Editar", "fileMenu": "&Arquivo", "forward": "&Avançar", "helpMenu": "<PERSON><PERSON>&uda", "hide": "Ocultar {{- appName}}", "hideOthers": "Ocultar <PERSON>", "learnMore": "<PERSON><PERSON> mais", "minimize": "<PERSON><PERSON><PERSON>", "openDevTools": "Abrir &DevTools", "openDevToolsOnAllWindows": "Abrir &DevTools em todas as janelas", "paste": "Co&lar", "quit": "&<PERSON><PERSON> do {{- appName}}", "redo": "&Refazer", "reload": "&Recarregar", "reloadClearingCache": "<PERSON><PERSON><PERSON>", "reportIssue": "Reportar problema", "resetAppData": "Limpar dados do aplicativo", "resetZoom": "Redefinir zoom", "selectAll": "&Selecionar tudo", "services": "Serviços", "showFullScreen": "Tela cheia", "showMenuBar": "Barra de menus", "showOnUnreadMessage": "<PERSON><PERSON>r quando há mensagens não lidas", "showServerList": "Lista de servidores", "showTrayIcon": "Ícone da bandeja", "toggleDevTools": "Alternar &DevTools", "undo": "&Desfazer", "unhide": "<PERSON><PERSON> todos", "viewMenu": "&Exibir", "windowMenu": "&<PERSON>la", "zoomIn": "Aumentar zoom", "zoomOut": "Diminuir zoom"}, "loadingError": {"title": "<PERSON><PERSON><PERSON>", "announcement": "Houston, nós temos um problema", "reload": "<PERSON><PERSON><PERSON><PERSON>"}, "videoCall": {"loading": {"initial": "Carregando chamada de vídeo...", "reloading": "Recarregando chamada de vídeo...", "description": "Por favor aguarde enquanto nos conectamos à chamada de vídeo"}, "error": {"title": "Falha ao carregar chamada de vídeo", "announcement": "Houston, temos um problema", "timeout": "Tempo limite excedido - a chamada de vídeo não pôde carregar em 15 segundos", "crashed": "Webview travou", "maxRetriesReached": "Falha ao carregar após múltiplas tentativas", "reload": "<PERSON><PERSON><PERSON><PERSON> chamada de vídeo"}}, "unsupportedServer": {"title": "{{instanceDomain}} está executando uma versão não suportada do Rocket.Chat", "announcement": "Um administrador precisa atualizar o workspace para uma versão suportada para reativar o acesso aos aplicativos móveis e desktop.", "moreInformation": "<PERSON><PERSON> mais"}, "selfxss": {"title": "Pare!", "description": "Esta é uma funcionalidade do navegador destinada aos desenvolvedores. Se alguém lhe disse para copiar-colar algo aqui para activar uma funcionalidade Rocket.Chat ou \"hackear\" a conta de alguém, é um golpe e dará acesso à sua conta Rocket.Chat.", "moreInfo": "<PERSON><PERSON><PERSON> https://go.rocket.chat/i/xss para mais informações."}, "sidebar": {"addNewServer": "Adicionar novo servidor", "downloads": "Downloads", "settings": "Configurações", "menuTitle": "Personalizar e controlar app", "item": {"reload": "<PERSON><PERSON><PERSON><PERSON>", "remove": "Remover", "openDevTools": "<PERSON><PERSON><PERSON> DevTools", "clearCache": "Limpar cache", "clearStorageData": "Limpar dados de armazenamento", "copyCurrentUrl": "Copiar URL", "reloadClearingCache": "<PERSON><PERSON><PERSON>"}, "tooltips": {"unreadMessage": "{{- count}} mensagem não lida", "unreadMessages": "{{- count}} mensagens não lidas", "userNotLoggedIn": "Não conectado", "addWorkspace": "Adicionar workspace ({{shortcut}}+N)", "settingsMenu": "Personalizar e controlar app"}}, "touchBar": {"formatting": "Formatação", "selectServer": "Selecionar servidor"}, "tray": {"menu": {"show": "Mostrar", "hide": "Esconder", "quit": "<PERSON><PERSON>"}, "tooltip": {"noUnreadMessage": "{{- appName}}: não há mensagens não lidas", "unreadMention": "{{- appName}}: você tem uma menção/mensagem direta não lida", "unreadMention_plural": "{{- appName}}: você tem {{- count}} menções/mensagens diretas não lidas", "unreadMessage": "{{- appName}}: você tem mensagens não lidas"}, "balloon": {"stillRunning": {"title": "{{- appName}} ainda está rodando", "content": "{{- appName }} está configurado para permanecer em funcionamento na bandeja do sistema/área de notificação."}}}, "taskbar": {"unreadMessage": "Mensagens não lidas", "unreadMention": "Menções não lidas", "noUnreadMessage": "<PERSON><PERSON><PERSON><PERSON> mensagem"}, "screenSharing": {"permissionDenied": "Permissão de Gravação de Tela Negada", "permissionRequired": "A permissão de gravação de tela é necessária para compartilhar sua tela.", "permissionInstructions": "Por favor, ative-a nas preferências do sistema e tente novamente.", "title": "Compartilhar sua tela", "entireScreen": "Toda sua tela", "applicationWindow": "Janela de aplicativo", "noScreensFound": "Nenhuma tela encontrada", "noWindowsFound": "<PERSON><PERSON><PERSON><PERSON> janela encontrada", "cancel": "<PERSON><PERSON><PERSON>", "share": "Compartilhar"}}