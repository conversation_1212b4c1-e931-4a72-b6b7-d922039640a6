{"contextMenu": {"cut": "В&ырезать", "copy": "&Копировать", "paste": "&Вставить", "selectAll": "Выделить все", "undo": "&Отменить", "redo": "&Повторить", "spelling": "Правописание", "spellingLanguages": "Языки орфографии", "moreSpellingSuggestions": "Другие варианты написания", "noSpellingSuggestions": "Нет предложений", "copyLinkAddress": "Копировать ссылку", "copyLinkText": "Копировать текст ссылки", "openLink": "Открыть ссылку", "saveImageAs": "Сохранить изображение как...", "copyImage": "Скопировать изображение"}, "dialog": {"about": {"title": "О {{- appName}}", "version": "Версия: <1>{{-version}}</1>", "checkUpdates": "Проверить обновления", "checkUpdatesOnStart": "Проверять обновления при запуске", "noUpdatesAvailable": "Нет обновлений.", "copyright": "Все права защищены {{copyright}}", "errorWhenLookingForUpdates": "При поиске обновлений произошла ошибка", "updateChannel": {"label": "Канал обновлений", "latest": "Стабильный", "beta": "Бета", "alpha": "Альфа (Экспериментальный)"}}, "addServer": {"title": "Добавить сервер", "message": "Вы хотите добавить \"{{- host}}\" в Ваш список серверов?", "add": "Добавить", "cancel": "Отменить"}, "addServerError": {"title": "Неверный Хост", "message": "Не удалось проверить хост \"{{- host}}\", поэтому он не был добавлен."}, "certificateError": {"title": "Ошибка сертификата", "message": "Вы доверяете сертификату от \"{{- issuerName}}\"?", "yes": "Да", "no": "Нет"}, "downloadRemoval": {"title": "Вы уверены?", "message": "Удалить эту загрузку?", "yes": "Да", "no": "Нет"}, "resetAppData": {"title": "Сброс данных приложения", "message": "Это осуществит выход из всех Ваших команд и вернет настройки по умолчанию. Это действие невозможно отменить.", "yes": "Да", "cancel": "Отменить"}, "screenshare": {"title": "Демонстрация Вашего экрана", "announcement": "Выберите экран для демонстрации"}, "update": {"title": "Доступно обновление", "announcement": "Доступно новое обновление", "message": "Доступна новая версия приложения Rocket.Chat!", "currentVersion": "Текущая версия:", "newVersion": "Новая версия:", "install": "Установить обновление", "remindLater": "Напомнить позже", "skip": "Пропустить эту версию"}, "updateDownloading": {"title": "Загрузка обновления", "message": "Вы будете оповещены, когда обновление будет готово к установке", "ok": "OK"}, "updateInstallLater": {"title": "Обновление будет установлено позже", "message": "Обновление будет установлено, когда Вы закроете приложение", "ok": "OK"}, "updateReady": {"title": "Обновление готово к установке", "message": "Обновление загружено", "installNow": "Установить сейчас", "installLater": "Установить позже"}, "updateSkip": {"title": "Пропустить обновление", "message": "Мы оповестим Вас, когда следующее обновление будет доступно.\nЕсли Вы передумаете, Вы можете проверить наличие обновлений в меню \"О Rocket.Chat\".", "ok": "OK"}, "selectClientCertificate": {"announcement": "Выберите сертификат", "select": "Выбрать", "validDates": "Действует от {{-validStart,}} до {{-validExpiry,}}"}, "openingExternalProtocol": {"title": "Связь с кастомным протоколом", "message": "ссылка для {{- protocol }} требует внешнего приложения.", "detail": "Запрашиваемая ссылка - {{- url }} . Вы хотите продолжить?", "dontAskAgain": "Всегда открывать ссылки данного типа в соответствующем приложении", "yes": "Да", "no": "Нет"}, "allowVideoCallCaptureScreen": {"title": "Видеовызов пытается захватить ваш экран", "message": "Видеозвонок запрашивает разрешение на захват вашего экрана", "detail": "Видеозвонок с сервера {{- url }} запрашивает разрешение на демонстрацию вашего экрана другим участникам", "dontAskAgain": "Всегда разрешать делать снимки экрана для видеовызовов с этого сервера", "yes": "Разрешить", "no": "Отмена"}, "mediaPermission": {"title": "Требуется Разрешение на Медиа", "message": "Доступ к {{- permissionType}} в настоящее время отключен в настройках вашей системы.", "detail": "Для включения функций видеозвонков разрешите доступ в настройках конфиденциальности вашей системы и перезапустите приложение.", "openSettings": "Открыть Настройки", "cancel": "Отмена", "microphone": "Микрофон", "camera": "Камера", "both": "Микрофон и Камера"}}, "downloads": {"title": "Загрузки", "notifications": {"downloadFinished": "Загрузка завершена", "downloadInterrupted": "Загрузка прервана", "downloadCancelled": "Загрузка отменена", "downloadFailed": "Скачать не удалось", "downloadExpired": "Срок загрузки истек", "downloadExpiredMessage": "Пожалуйста, повторите попытку загрузки из источника."}, "filters": {"search": "Поиск", "server": "Сервер", "mimeType": "Тип", "status": "Статус", "clear": "Очистить фильтры", "all": "Все", "mimes": {"images": "Изображения", "videos": "Видео", "audios": "Аудиозаписи", "texts": "Тексты", "files": "Файлы"}, "statuses": {"paused": "На паузе", "cancelled": "Отменено"}}, "item": {"cancel": "Отменить", "copyLink": "Копировать ссылку", "errored": "Загрузка отменена", "pause": "Пауза", "progressSize": "{{receivedBytes, byteSize}} из {{totalBytes, byteSize}} ({{ratio, percentage}})", "remove": "Убрать из списка", "resume": "Возобновить", "retry": "Повторить", "showInFolder": "Показать в папке"}, "showingResults": "Показать результаты {{first}} - {{last}} из {{count}}"}, "certificatesManager": {"title": "Менед<PERSON>ер сертифи<PERSON><PERSON><PERSON>ов", "trustedCertificates": "Доверенные сертификаты", "notTrustedCertificates": "Не доверенные сертификаты", "item": {"domain": "До<PERSON><PERSON>н", "actions": "Действия", "remove": "Удалить"}}, "settings": {"title": "Настройки", "general": "Общее", "certificates": "Сертификаты", "options": {"report": {"title": "Сообщать об ошибках разработчикам", "description": "Анонимно сообщайте об ошибках разработчикам. Передаваемая информация включает номер версии приложения, тип операционной системы, URL-адрес сервера, язык устройства и тип ошибки. Содержание чатов и имена пользователей не передаются.", "masDescription": "Эта опция отключена при установке из Mac App Store, ошибки будут через механизм сбора ошибок Mac App Store."}, "flashFrame": {"title": "Включить мигание иконки", "titleDarwin": "Мигать значком в трее при оповещении", "description": "Данная опция управляет включением и выключением мигания иконки приложения при поступлении новых сообщений", "onLinux": "Некоторые дистрибутивы Linux не поддерживают эту функцию.", "descriptionDarwin": "Мигание значка приложения в трее для привлечения внимания пользователя."}, "hardwareAcceleration": {"title": "Аппаратное ускорение", "description": "Включает использование аппаратного ускорения, если оно доступно. Приложение будет перезапущено при изменении этой настройки."}, "internalVideoChatWindow": {"title": "Открывать видеочат в окне приложения", "description": "Если этот параметр включен, видеочат будет открываться в окне приложения вместо браузера по умолчанию. Однако для <strong>Google Meet</strong> и <strong>Ji<PERSON>i</strong> запись экрана не поддерживается в приложениях Electron, поэтому они всегда будут открываться в браузере независимо от этой настройки.", "masDescription": "Эта опция отключена при установке из Mac App Store, по соображениям безопасности окно видео чата будет открыто в браузере по умолчанию."}, "minimizeOnClose": {"title": "Свернуть в трей при закрытии", "description": "При закрытии приложения оно будет свернуто в системный трей, иначе последует выход из приложения."}, "menubar": {"title": "Главное меню", "description": "Показать строку меню в верхней части окна.", "disabledHint": "Нельзя отключить строку меню, когда боковая панель уже отключена. Настройки станут недоступными."}, "sidebar": {"title": "Боковая панель", "description": "Показать боковую панель в левой части окна со списком серверов, загрузками и настройками.", "disabledHint": "Нельзя отключить боковую панель, когда строка меню уже отключена. Настройки станут недоступными."}, "trayIcon": {"title": "Значок в трее", "description": "Показывать значок в системном трее. Если значок в трее активен, приложение будет свернуто в трей при закрытии. В противном случае приложение будет завершено."}, "availableBrowsers": {"title": "Браузер по умолчанию", "description": "Выберите, какой браузер будет открывать внешние ссылки из этого приложения. Системный по умолчанию использует настройки вашей операционной системы.", "systemDefault": "Системный по умолчанию", "loading": "Загрузка браузеров...", "current": "Сейчас используется:"}, "clearPermittedScreenCaptureServers": {"title": "Очистить разрешенные серверы захвата экрана", "description": "Выберите серверы, которые могут захватывать экраны приложений из этого приложения."}, "ntlmCredentials": {"title": "Учетные данные NTLM", "description": "Разрешить использование учетных данных NTLM при подключении к серверу.", "domains": "Домены, которые будут использовать учетные данные. Разделены запятыми. Используйте * для соответствия всем доменам."}, "videoCallWindowPersistence": {"title": "Запоминать положение окна видеозвонка", "description": "Сохранять и восстанавливать положение и размер окон видеозвонков между сеансами"}}}, "error": {"authNeeded": "Требуется авторизация, попробуйте <strong>{{- auth}}</strong>", "connectTimeout": "Превышено время ожидания при попытке подключения", "differentCertificate": "Сертификат отличается от предыдущего.\n\n {{- detail}}", "noValidServerFound": "По заданному URL не найден подходящий сервер", "offline": "Проверьте Ваше интернет-соединение!"}, "landing": {"invalidUrl": "Некорректный url", "validating": "Проверяем...", "inputUrl": "Введите URL Вашего сервера", "connect": "Подключиться"}, "menus": {"about": "О {{- appName}}", "addNewServer": "Добавить &новый сервер", "back": "&Назад", "clearTrustedCertificates": "Очистить доверенные сертификаты", "close": "Закрыть", "copy": "&Копировать", "cut": "В&ырезать", "developerMode": "Режим разработчика", "disableGpu": "Отключить GPU", "documentation": "Документация", "downloads": "Загрузки", "settings": "Настройки", "editMenu": "&Правка", "fileMenu": "&<PERSON><PERSON><PERSON><PERSON>", "forward": "Вперед", "helpMenu": "&Справка", "hide": "Скрыть {{- appName}}", "hideOthers": "Скрыть других", "learnMore": "Узнать больше", "minimize": "Свернуть", "openDevTools": "Открыть &DevTools", "openDevToolsOnAllWindows": "Открыть &DevTools во всех окнах", "paste": "&Вставить", "quit": "Вы&йти из {{- appName}}", "redo": "&Повторить", "reload": "П&ерезагрузить", "reportIssue": "Сообщить о проблеме", "resetAppData": "Сбросить данные приложения", "resetZoom": "Восстановить масштаб", "selectAll": "Выделить все", "services": "Сервисы", "showFullScreen": "Полноэкранный режим", "showMenuBar": "Главное меню", "showOnUnreadMessage": "Развернуть при поступлении новых сообщений", "showServerList": "Список серверов", "showTrayIcon": "Значок в трее", "toggleDevTools": "Открыть &DevTools", "undo": "&Отменить", "unhide": "Показать все", "viewMenu": "&Вид", "windowMenu": "&Окна", "zoomIn": "Увеличить", "zoomOut": "Уменьшить"}, "loadingError": {"title": "Не удалось загрузить сервер", "announcement": "Хьюстон, у нас проблемы", "reload": "Перезагрузить"}, "videoCall": {"loading": {"initial": "Загрузка видеозвонка...", "reloading": "Перезагрузка видеозвонка...", "description": "Пожалуйста, подождите, пока мы подключаемся к видеозвонку"}, "error": {"title": "Не удалось загрузить видеозвонок", "announcement": "Хью<PERSON><PERSON>он, у нас проблема", "timeout": "Превышено время ожидания - видеозвонок не удалось загрузить за 15 секунд", "crashed": "Webview завис", "maxRetriesReached": "Не удалось загрузить после нескольких попыток", "reload": "Перезагрузить видеозвонок"}}, "selfxss": {"title": "Стоп!", "description": "Это функция браузера, предназначенная для разработчиков. Если кто-то сказал вам скопировать-вставить что-то сюда, чтобы включить функцию Rocket.Chat или \"взломать\" чей-то аккаунт, это мошенничество и даст им доступ к вашему аккаунту Rocket.Chat.", "moreInfo": "Дополнительная информация доступна по адресу https://go.rocket.chat/i/xss."}, "sidebar": {"addNewServer": "Добавить новый сервер", "downloads": "Загрузки", "settings": "Настройки", "item": {"reload": "Перезагрузить вкладку сервера", "remove": "Удалить сервер", "openDevTools": "Открыть DevTools", "clearCache": "Очистить кэш", "clearStorageData": "Очистить данные хранилища"}}, "touchBar": {"formatting": "Форматирование", "selectServer": "Выбрать сервер"}, "tray": {"menu": {"show": "Показать", "hide": "Скрыть", "quit": "Выйти"}, "tooltip": {"noUnreadMessage": "{{- appName}}: нет непрочитанных сообщений", "unreadMention": "{{- appName}}: у вас есть непрочитанное упоминание/личное сообщение", "unreadMention_plural": "{{- appName}}: у вас есть {{- count}} непрочитанных упоминаний/личных сообщений", "unreadMessage": "{{- appName}}: у Вас непрочитанных сообщений"}, "balloon": {"stillRunning": {"title": "{{- appName}} все еще работает", "content": "{{- appName }} настроен на постоянное размещение в системном трее/области уведомлений."}}}, "taskbar": {"unreadMessage": "Непрочитанные сообщения", "unreadMention": "Непрочитанные упоминания", "noUnreadMessage": "Нет непрочитанных сообщений"}, "screenSharing": {"permissionDenied": "Разрешение на запись экрана отклонено", "permissionRequired": "Для демонстрации экрана требуется разрешение на запись экрана.", "permissionInstructions": "Пожалуйста, включите его в настройках системы и попробуйте снова.", "title": "Демонстрация экрана", "entireScreen": "Весь экран", "applicationWindow": "Окно приложения", "noScreensFound": "Экраны не найдены", "noWindowsFound": "Окна не найдены", "cancel": "Отмена", "share": "Демонстрировать"}}