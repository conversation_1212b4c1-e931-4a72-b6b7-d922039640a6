{"contextMenu": {"cut": "剪切 (&T)", "copy": "复制 (&P)", "paste": "粘贴 (&P)", "selectAll": "全选 (&A)", "undo": "复原 (&U)", "redo": "重做 (&R)", "spellingLanguages": "拼写检查", "moreSpellingSuggestions": "其他拼写建议", "noSpellingSuggestions": "没有拼写建议", "copyLinkAddress": "复制链接URL", "copyLinkText": "复制链接文字", "openLink": "打开链接", "saveImageAs": "保存图片...", "copyImage": "复制图片"}, "dialog": {"about": {"title": "关于 {{- appName}}", "version": "版本: <1>{{-version}}</1>", "checkUpdates": "检查更新", "checkUpdatesOnStart": "启动时检查更新", "noUpdatesAvailable": "目前沒有可用的更新", "copyright": "版权 {{copyright}}"}, "addServer": {"title": "新增服务器", "message": "您是否要将 \"{{- host}}\" 新增到您的服务器列表中？", "add": "新增", "cancel": "取消"}, "addServerError": {"title": "无效的网址", "message": "网址 \"{{- host}}\" 无法验证，因此沒有新增。"}, "certificateError": {"title": "证书错误", "message": "您是否要信任來自 \"{{- issuerName}}\" 的证书？", "yes": "是", "no": "否"}, "resetAppData": {"title": "重置应用数据", "message": "您可以退出所有团队并将应用程序恢复为其原始设置。这将无法撤消。", "yes": "是", "cancel": "取消"}, "screenshare": {"title": "分享您的画面", "announcement": "选择要分享的画面"}, "update": {"title": "可用的更新", "announcement": "有新的更新程序", "message": "有新版本的 Rocket.Chat 桌面应用程序！", "currentVersion": "目前版本:", "newVersion": "最新版本:", "install": "安裝更新", "remindLater": "稍后再提醒我", "skip": "忽略这个版本"}, "updateDownloading": {"title": "正在下载更新", "message": "当准备好可以安装更新程序的时候，您将会收到通知。", "ok": "好"}, "updateInstallLater": {"title": "稍后再安裝", "message": "结束程序的时候将会安装更新", "ok": "OK"}, "updateReady": {"title": "已准备好安装更新", "message": "更新程序已下载完毕", "installNow": "立刻安裝", "installLater": "稍后安裝"}, "updateSkip": {"title": "忽略更新", "message": "我们将会在下次有新的更新版本的时候通知您\n如果您改变主意想安装此处更新，您可以从「关于」的选项中检查更新。", "ok": "好"}, "mediaPermission": {"title": "需要媒体权限", "message": "{{- permissionType}}访问目前在您的系统设置中已被禁用。", "detail": "要启用视频通话功能，请在您的系统隐私设置中允许访问，然后重新启动应用程序。", "openSettings": "打开设置", "cancel": "取消", "microphone": "麦克风", "camera": "摄像头", "both": "麦克风和摄像头"}}, "downloads": {"title": "下载", "filters": {"search": "搜索", "server": "服务器", "mimeType": "类型", "status": "状态", "clear": "清除筛选器", "all": "全部", "mimes": {"images": "图片", "videos": "视频", "audios": "音频", "texts": "文本", "files": "文件"}, "statuses": {"paused": "已暂停", "cancelled": "已取消"}}, "item": {"cancel": "取消", "copyLink": "复制链接", "errored": "下载已取消", "pause": "暂停", "progressSize": "{{receivedBytes, byteSize}} of {{totalBytes, byteSize}} ({{ratio, percentage}})", "remove": "从列表中删除", "resume": "暂停", "retry": "重试", "showInFolder": "在文件夹中显示"}, "showingResults": "显示{{count}}个中的第{{first}} - {{last}}个"}, "settings": {"title": "设置", "options": {"report": {"title": "向开发者报告错误", "description": "匿名向开发者报告错误。会发送包括程序版本号、操作系统类型、服务器地址、设备语言和错误类型。不会发送聊天内容和用户名等信息。"}, "flashFrame": {"title": "允许闪屏", "description": "使用闪屏来吸引用户的注意。", "onLinux": "在一些Linux系统上这个功能不可用。"}, "internalVideoChatWindow": {"title": "在程序窗口中打开视频通话", "description": "如果启用，视频通话将在应用程序窗口中打开，而不是在默认浏览器中打开。但是，对于<strong>Google Meet</strong>和<strong>Jitsi</strong>，Electron应用程序不支持屏幕录制，所以无论此设置如何，它们始终会在浏览器中打开。", "masDescription": "从Mac App Store安装时此选项被禁用。出于安全原因，视频通话将始终默认在浏览器中打开。"}}}, "error": {"authNeeded": "需要验证，请重新验证 <strong>{{- auth}}</strong>", "connectTimeout": "连接超时", "differentCertificate": "证书与之前的不同。\n\n {{- detail}}", "noValidServerFound": "在这个网址找不到有效的服务器", "offline": "请检查您的网络连接！"}, "landing": {"invalidUrl": "无效的网址", "validating": "验证中...", "inputUrl": "请输入您的服务器网址", "connect": "连接"}, "menus": {"about": "关于 {{- appName}}", "addNewServer": "新增服务器", "back": "上一步 (&B)", "clearTrustedCertificates": "清除已信任的证书", "close": "关闭", "copy": "复制 (&C)", "cut": "剪切 (&T)", "developerMode": "开发者模式", "disableGpu": "禁用 GPU", "documentation": "文件", "downloads": "下载", "settings": "设置", "editMenu": "编辑 (&E)", "fileMenu": "文件 (&F)", "forward": "下一步 (&F)", "helpMenu": "帮助 (&H)", "learnMore": "了解更多", "minimize": "最小化", "openDevTools": "开启开发工具 (&D)", "paste": "粘贴 (&P)", "quit": "结束(&Q){{- appName}}", "redo": "重做 (&R)", "reload": "重新载入 (&R)", "reportIssue": "报告问题", "resetAppData": "重置应用数据", "resetZoom": "重置缩放", "selectAll": "选择全部 (&A)", "showFullScreen": "全屏幕", "showMenuBar": "选项", "showOnUnreadMessage": "显示未读取的信息", "showServerList": "服务器列表", "showTrayIcon": "托盘图标", "toggleDevTools": "切换开发工具 (&D)", "undo": "还原 (&U)", "viewMenu": "显示 (&V)", "windowMenu": "窗口 (&W)", "zoomIn": "放大", "zoomOut": "缩小"}, "loadingError": {"title": "服务器载入失败", "announcement": "稍等一下，我们遇到问题了", "reload": "重新載入"}, "videoCall": {"loading": {"initial": "正在加载视频通话...", "reloading": "正在重新加载视频通话...", "description": "请稍候，我们正在连接到视频通话"}, "error": {"title": "视频通话加载失败", "announcement": "休斯顿，我们遇到了问题", "timeout": "加载超时 - 视频通话无法在15秒内加载", "crashed": "网页视图崩溃了", "maxRetriesReached": "多次尝试后加载失败", "reload": "重新加载视频通话"}}, "sidebar": {"addNewServer": "新增服务器", "downloads": "下载", "settings": "设置", "item": {"reload": "重新载入服务器", "remove": "移除服务器", "openDevTools": "开启开发工具", "clearCache": "清除缓存", "clearStorageData": "清除存储数据"}}, "touchBar": {"formatting": "格式化", "selectServer": "选择服务器"}, "tray": {"menu": {"show": "显示", "hide": "隐藏", "quit": "结束"}, "tooltip": {"noUnreadMessage": "{{- appName}}: 没有未读的信息", "unreadMention": "{{- appName}}: 您有未读的直接或提及到您的信息", "unreadMention_plural": "{{- appName}}: 您有 {{- count}} 封未读的直接或提及到您的信息", "unreadMessage": "{{- appName}}: 您有未读的信息"}}, "taskbar": {"unreadMessage": "未读消息", "unreadMention": "未读提及", "noUnreadMessage": "没有未读消息"}, "screenSharing": {"permissionDenied": "屏幕录制权限被拒绝", "permissionRequired": "需要屏幕录制权限才能共享您的屏幕。", "permissionInstructions": "请在系统偏好设置中启用它，然后重试。", "title": "共享您的屏幕", "entireScreen": "您的整个屏幕", "applicationWindow": "应用程序窗口", "noScreensFound": "未找到屏幕", "noWindowsFound": "未找到窗口", "cancel": "取消", "share": "共享"}}