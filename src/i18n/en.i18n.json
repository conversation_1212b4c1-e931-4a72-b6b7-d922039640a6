{"contextMenu": {"cut": "Cu&t", "copy": "&Copy", "paste": "&Paste", "selectAll": "Select &all", "undo": "&Undo", "redo": "&Redo", "spelling": "Spelling", "spellingLanguages": "Spelling languages", "moreSpellingSuggestions": "More spelling suggestions", "noSpellingSuggestions": "No suggestions", "copyLinkAddress": "Copy link address", "copyLinkText": "Copy link text", "openLink": "Open link", "saveImageAs": "Save image as...", "copyImage": "Copy image"}, "dialog": {"about": {"title": "About {{- appName}}", "version": "Version: <1>{{-version}}</1>", "checkUpdates": "Check for Updates", "checkUpdatesOnStart": "Check for Updates on Start", "noUpdatesAvailable": "No updates are available.", "copyright": "Copyright {{copyright}}", "errorWhenLookingForUpdates": "An error has occurred when looking for updates", "updateChannel": {"label": "Update Channel", "latest": "Stable", "beta": "Beta", "alpha": "Alpha (Experimental)"}}, "addServer": {"title": "Add Server", "message": "Do you want to add \"{{- host}}\" to your list of servers?", "add": "Add", "cancel": "Cancel"}, "addServerError": {"title": "Invalid Host", "message": "The host \"{{- host}}\" could not be validated, so was not added."}, "certificateError": {"title": "Certificate error", "message": "Do you trust certificate from \"{{- issuerName}}\"? Only click 'Yes' if you really trust this certificate. If you are unsure, contact your IT or security team before clicking on 'Yes'.", "yes": "Yes", "no": "No"}, "clearCache": {"announcement": "Force reload", "title": "Keep your login session?", "message": "By deleting your login session you will be logged out and asked to inform your credentials again.", "keepLoginData": "Keep login session", "deleteLoginData": "Delete login session", "clearingWait": "Please wait", "cancel": "Cancel"}, "downloadRemoval": {"title": "Are you sure?", "message": "Remove this download?", "yes": "Yes", "no": "No"}, "resetAppData": {"title": "Reset app data", "message": "This will sign you out from all your teams and reset the app back to its original settings. This cannot be undone.", "yes": "Yes", "cancel": "Cancel"}, "clearPermittedScreenCaptureServers": {"title": "Clear Permitted Screen Capture Servers", "message": "This will clear all the permissions for screen capture servers, making them ask again for permission. This cannot be undone.", "yes": "Yes", "cancel": "Cancel"}, "screenshare": {"title": "Share Your Screen", "announcement": "Select a screen to share"}, "update": {"title": "Update Available", "announcement": "New Update is Available", "message": "A new version of the Rocket.Chat Desktop App is available!", "currentVersion": "Current Version:", "newVersion": "New Version:", "install": "Install Update", "remindLater": "Remind Me Later", "skip": "Skip This Version"}, "updateDownloading": {"title": "Downloading Update", "message": "You will be notified when the update is ready to be installed", "ok": "OK"}, "updateInstallLater": {"title": "Installing Later", "message": "Update will be installed when you exit the app", "ok": "OK"}, "updateReady": {"title": "Update Ready to Install", "message": "Update has been downloaded", "installNow": "Install Now", "installLater": "Install Later"}, "updateSkip": {"title": "Skip Update", "message": "We will notify you when the next update is available\nIf you change your mind you can check for updates from the About menu.", "ok": "OK"}, "selectClientCertificate": {"announcement": "Select Certificate", "select": "Select", "validDates": "Valid from {{-validStart,}} to {{-validExpiry,}}"}, "openingExternalProtocol": {"title": "Link with custom protocol", "message": "{{- protocol }} link requires an external application.", "detail": "The requested link is {{- url }} . Do you want to continue?", "dontAskAgain": "Always open these types of links in the associated app", "yes": "Yes", "no": "No"}, "allowVideoCallCaptureScreen": {"title": "The video call is trying to capture your screen", "message": "The video call is requesting permission to capture your screen.", "detail": "The video call from the server {{- url }} requires permission in order to share your screen with others.", "dontAskAgain": "Always allow video calls from this server to capture your screen", "yes": "Allow", "no": "Cancel"}, "mediaPermission": {"title": "Media Permission Required", "message": "{{- permissionType}} access is currently disabled in your system settings.", "detail": "To enable video calling features, please allow access in your system's privacy settings and then restart the application.", "openSettings": "Open Settings", "cancel": "Cancel", "microphone": "Microphone", "camera": "Camera", "both": "Microphone and Camera"}, "outlookCalendar": {"title": "Outlook Calendar", "encryptionUnavailableTitle": "Encryption unavailable", "encryptionUnavailable": "Your operational system don't support encryption.\nYour credentials will be stored in plain text.", "field_required": "This field is required", "remember_credentials": "Remember my credentials", "cancel": " Cancel", "submit": "<PERSON><PERSON>"}, "supportedVersion": {"title": "Workspace version unsupported"}}, "downloads": {"title": "Downloads", "notifications": {"downloadFinished": "Download Finished", "downloadInterrupted": "Download Interrupted", "downloadCancelled": "Download Cancelled", "downloadFailed": "Download Failed", "downloadExpired": "<PERSON><PERSON><PERSON> Expired", "downloadExpiredMessage": "Please retry downloading from source."}, "filters": {"search": "Search", "server": "Server", "mimeType": "Type", "status": "Status", "clear": "Clear filters", "all": "All", "mimes": {"images": "Images", "videos": "Videos", "audios": "Audios", "texts": "Texts", "files": "Files"}, "statuses": {"paused": "Paused", "cancelled": "Cancelled"}}, "item": {"cancel": "Cancel", "copyLink": "Copy link", "errored": "Download cancelled", "pause": "Pause", "progressSize": "{{receivedBytes, byteSize}} of {{totalBytes, byteSize}} ({{ratio, percentage}})", "remove": "Remove from list", "resume": "Resume", "retry": "Retry", "showInFolder": "Show in Folder"}, "showingResults": "Showing results {{first}} - {{last}} of {{count}}"}, "certificatesManager": {"title": "Certificates manager", "trustedCertificates": "Trusted certificates", "notTrustedCertificates": "Not trusted certificates", "item": {"domain": "Domain", "actions": "Actions", "remove": "Remove"}}, "settings": {"title": "Settings", "general": "General", "certificates": "Certificates", "options": {"report": {"title": "Report errors to developers", "description": "Report errors anonymously to the developers. Shared information include app version number, operating system type, server URL, device language and error type. No content or usernames are shared.", "masDescription": "This option is disabled when installed from the Mac App Store, the errors will be reported through the Mac App Store error report process."}, "flashFrame": {"title": "Enable Flash <PERSON>ame", "titleDarwin": "Toggle Dock Bounce on alert", "description": "Flashes the window to attract user's attention.", "onLinux": "Some Linux distros don't have support for this feature.", "descriptionDarwin": "Bounces the app icon in the dock to attract user's attention."}, "hardwareAcceleration": {"title": "Hardware Acceleration", "description": "Enables the use of hardware acceleration when available. The application will reload on change."}, "internalVideoChatWindow": {"title": "Open Video Chat in Application Window", "description": "If enabled, the Video Chat will open in the application's window instead of the default browser. However, for <strong>Google Meet</strong> and <strong>Jitsi</strong>, screen recording is not supported in Electron applications, so they will always open in the browser regardless of this setting.", "masDescription": "This option is disabled when installed from the Mac App Store. For security reasons, Video Chat will always open in the browser by default."}, "minimizeOnClose": {"title": "Minimize on close", "description": "When closed the app will be minimized, otherwise it will quit the application. Tray Icon need to be disabled to this take effect."}, "menubar": {"title": "Menu bar", "description": "Show menu bar on the top of the window.", "disabledHint": "Cannot disable menu bar when sidebar is disabled. Settings would become inaccessible."}, "sidebar": {"title": "Sidebar", "description": "Show sidebar on the left of the window with the Server List, Downloads and Settings.", "disabledHint": "Cannot disable sidebar when menu bar is disabled. Settings would become inaccessible."}, "trayIcon": {"title": "Tray Icon", "description": "Show tray icon on the system tray. If tray icon is active the app will be hidden to tray on close. Otherwise it will quit the application."}, "availableBrowsers": {"title": "<PERSON><PERSON><PERSON>", "description": "Choose which browser will open external links from this app. System Default uses your operating system settings.", "systemDefault": "System Default", "loading": "Loading browsers...", "current": "Currently using:"}, "clearPermittedScreenCaptureServers": {"title": "Clear Screen Capture Permissions", "description": "Clear the screen capture permissions that was selected to not ask again on video calls."}, "allowScreenCaptureOnVideoCalls": {"title": "Allow Screen Capture on Video Calls", "description": "Allow screen capture on video calls. This will ask for permission on each video call."}, "ntlmCredentials": {"title": "NTLM Credentials", "description": "Allow NTLM Credentials to be used when connecting to a server.", "domains": "Domains that will use the credentials. Separated by comma. Use * to match all domains."}, "videoCallWindowPersistence": {"title": "Remember video call window position", "description": "Save and restore the position and size of video call windows between sessions"}}}, "error": {"authNeeded": "Auth needed, try <strong>{{- auth}}</strong>", "connectTimeout": "Timeout trying to connect", "differentCertificate": "Certificate is different from previous one.\n\n {{- detail}}", "noValidServerFound": "No valid server found at the URL", "offline": "Check your Internet connection!"}, "landing": {"invalidUrl": "Invalid url", "validating": "Validating...", "inputUrl": "Enter your server URL", "connect": "Connect"}, "menus": {"about": "About {{- appName}}", "addNewServer": "Add &new server", "back": "&Back", "clearTrustedCertificates": "Clear trusted certificates", "close": "Close", "copy": "&Copy", "cut": "Cu&t", "developerMode": "Developer Mode", "disableGpu": "Disable GPU", "documentation": "Documentation", "downloads": "Downloads", "settings": "Settings", "editMenu": "&Edit", "fileMenu": "&File", "forward": "&Forward", "helpMenu": "&Help", "hide": "Hide {{- appName}}", "hideOthers": "Hide Others", "learnMore": "Learn more", "minimize": "Minimize", "openDevTools": "Open &DevTools", "openDevToolsOnAllWindows": "Open &DevTools on all windows", "paste": "&Paste", "quit": "&Quit {{- appName}}", "redo": "&Redo", "reload": "&Reload", "reloadClearingCache": "Force reload", "reportIssue": "Report issue", "resetAppData": "Reset app data", "resetZoom": "Reset zoom", "selectAll": "Select &all", "services": "Services", "showFullScreen": "Full screen", "showMenuBar": "Menu bar", "showOnUnreadMessage": "Show on unread messages", "showServerList": "Server list", "showTrayIcon": "Tray icon", "toggleDevTools": "Toggle &DevTools", "openConfigFolder": "Open &Configuration Folder", "videoCallDevTools": "Open Video Call &DevTools", "videoCallTools": "Video Call Tools", "videoCallDevToolsAutoOpen": "Auto-open DevTools", "undo": "&Undo", "unhide": "Show All", "viewMenu": "&View", "windowMenu": "&Window", "zoomIn": "Zoom in", "zoomOut": "Zoom out"}, "loadingError": {"title": "Server Failed to Load", "announcement": "Houston, we have a problem", "reload": "Reload"}, "videoCall": {"loading": {"initial": "Loading video call...", "reloading": "Reloading video call...", "description": "Please wait while we connect to the video call"}, "error": {"title": "Video Call Failed to Load", "announcement": "Houston, we have a problem", "timeout": "Loading timeout - video call failed to load within 15 seconds", "crashed": "Webview crashed", "maxRetriesReached": "Failed to load after multiple attempts", "reload": "Reload Video Call"}}, "unsupportedServer": {"title": "{{instanceDomain}} is running an unsupported version of Rocket.Chat", "announcement": "An admin needs to update the workspace to a supported version in order to reenable access from mobile and desktop apps.", "moreInformation": "Learn more"}, "selfxss": {"title": "Stop!", "description": "This is a browser feature intended for developers. If someone told you to copy-paste something here to enable a Rocket.Chat feature or \"hack\" someone's account, it is a scam and will give them access to your Rocket.Chat account.", "moreInfo": "See https://go.rocket.chat/i/xss for more information."}, "sidebar": {"addNewServer": "Add new server", "downloads": "Downloads", "settings": "Settings", "menuTitle": "Customize and control app", "item": {"reload": "Reload", "remove": "Remove", "openDevTools": "Open DevTools", "clearCache": "<PERSON>ache", "clearStorageData": "Clear Storage Data", "copyCurrentUrl": "Copy current URL", "reloadClearingCache": "Force reload", "serverInfo": "Server Info", "supportedVersionsInfo": "Supported Versions Info"}, "tooltips": {"unreadMessage": "{{- count}} unread message", "unreadMessages": "{{- count}} unread messages", "userNotLoggedIn": "Not logged in", "addWorkspace": "Add workspace ({{shortcut}}+N)", "settingsMenu": "Customize and control app"}}, "touchBar": {"formatting": "Formatting", "selectServer": "Select server"}, "tray": {"menu": {"show": "Show", "hide": "<PERSON>de", "quit": "Quit"}, "tooltip": {"noUnreadMessage": "{{- appName}}: no unread message", "unreadMention": "{{- appName}}: you have a unread mention/direct message", "unreadMention_plural": "{{- appName}}: you have {{- count}} unread mentions/direct messages", "unreadMessage": "{{- appName}}: you have unread messages"}, "balloon": {"stillRunning": {"title": "{{- appName}} is still running", "content": "{{- appName }} is set to stay running in the system tray/notification area."}}}, "taskbar": {"unreadMessage": "Unread messages", "unreadMention": "Unread mentions", "noUnreadMessage": "No unread messages"}, "screenSharing": {"permissionDenied": "Screen Recording Permission Denied", "permissionRequired": "Screen recording permission is required to share your screen.", "permissionInstructions": "Please enable it in your system preferences and try again.", "title": "Share your screen", "entireScreen": "Your entire screen", "applicationWindow": "Application window", "noScreensFound": "No screens found", "noWindowsFound": "No windows found", "cancel": "Cancel", "share": "Share"}}