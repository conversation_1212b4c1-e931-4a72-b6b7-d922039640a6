{"contextMenu": {"cut": "Cor&tar", "copy": "&Copiar", "paste": "&<PERSON><PERSON><PERSON>", "selectAll": "Seleccionar &todo", "undo": "&Deshacer", "redo": "&<PERSON>hacer", "spelling": "Ortografía", "spellingLanguages": "Idiomas de ortografía", "moreSpellingSuggestions": "Más sugerencias de ortografía", "noSpellingSuggestions": "<PERSON> sugerencias", "copyLinkAddress": "Copiar dirección del enlace", "copyLinkText": "Copiar texto del enlace", "openLink": "<PERSON><PERSON><PERSON> enlace", "saveImageAs": "Guardar imagen como...", "copyImage": "Copiar imagen"}, "dialog": {"about": {"title": "Acerca de {{- appName}}", "version": "Versión: <1>{{-version}}</1>", "checkUpdates": "Buscar actualizaciones", "checkUpdatesOnStart": "Buscar actualizaciones al iniciar", "noUpdatesAvailable": "No hay actualizaciones disponibles.", "copyright": "Derechos de autor {{copyright}}", "errorWhenLookingForUpdates": "Ha ocurrido un error al buscar actualizaciones", "updateChannel": {"label": "Canal de actualización", "latest": "Estable", "beta": "Beta", "alpha": "Alfa (Experimental)"}}, "addServer": {"title": "Ag<PERSON><PERSON> servidor", "message": "¿Deseas agregar \"{{- host}}\" a tu lista de servidores?", "add": "Agregar", "cancel": "<PERSON><PERSON><PERSON>"}, "addServerError": {"title": "Host no válido", "message": "El host \"{{- host}}\" no pudo ser validado y no fue agregado."}, "certificateError": {"title": "Error de certificado", "message": "¿Confías en el certificado de \"{{- issuerName}}\"?", "yes": "Sí", "no": "No"}, "downloadRemoval": {"title": "¿Estás seguro?", "message": "¿Deseas eliminar esta descarga?", "yes": "Sí", "no": "No"}, "resetAppData": {"title": "Restablecer datos de la aplicación", "message": "Esto cerrará tu sesión en todos tus equipos y restaurará la aplicación a su configuración original. Esto no se puede deshacer.", "yes": "Sí", "cancel": "<PERSON><PERSON><PERSON>"}, "clearPermittedScreenCaptureServers": {"title": "Borrar permisos de captura de pantalla", "message": "Esto borrará todos los permisos para los servidores de captura de pantalla, haciendo que se soliciten nuevamente. Esto no se puede deshacer.", "yes": "Sí", "cancel": "<PERSON><PERSON><PERSON>"}, "screenshare": {"title": "Compartir tu pantalla", "announcement": "Selecciona una pantalla para compartir"}, "update": {"title": "Actualización disponible", "announcement": "Nueva actualización disponible", "message": "¡Hay una nueva versión de la aplicación de Rocket.Chat Desktop disponible!", "currentVersion": "Versión actual:", "newVersion": "Nueva versión:", "install": "Instalar actualización", "remindLater": "Recordarme más tarde", "skip": "Saltar esta versión"}, "updateDownloading": {"title": "Descargando actualización", "message": "Serás notificado cuando la actualización esté lista para ser instalada", "ok": "Aceptar"}, "updateInstallLater": {"title": "Instalar más tarde", "message": "La actualización se instalará cuando cierres la aplicación", "ok": "Aceptar"}, "updateReady": {"title": "Actualización lista para instalar", "message": "La actualización ha sido descargada", "installNow": "<PERSON><PERSON><PERSON> ahora", "installLater": "Instalar más tarde"}, "updateSkip": {"title": "Saltar actualización", "message": "Te notificaremos cuando esté disponible la próxima actualización.\nSi cambias de opinión, puedes buscar actualizaciones desde el menú Acerca de.", "ok": "Aceptar"}, "selectClientCertificate": {"announcement": "Seleccionar certificado", "select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "validDates": "<PERSON><PERSON><PERSON><PERSON> {{-validStart,}} hasta {{-validExpiry,}}"}, "openingExternalProtocol": {"title": "Enlace con protocolo personalizado", "message": "El enlace {{- protocol }} requiere una aplicación externa.", "detail": "El enlace solicitado es {{- url }}. ¿Deseas continuar?", "dontAskAgain": "Siempre abrir estos tipos de enlaces en la aplicación asociada", "yes": "Sí", "no": "No"}, "allowVideoCallCaptureScreen": {"title": "La videollamada intenta capturar tu pantalla", "message": "La videollamada está solicitando permiso para capturar tu pantalla.", "detail": "La videollamada del servidor {{- url }} requiere permiso para compartir tu pantalla con otros.", "dontAskAgain": "Siempre permitir que las videollamadas de este servidor capturen tu pantalla", "yes": "<PERSON><PERSON><PERSON>", "no": "<PERSON><PERSON><PERSON>"}, "mediaPermission": {"title": "Permiso de Medios Requerido", "message": "El acceso al {{- permissionType}} está deshabilitado en la configuración de tu sistema.", "detail": "Para habilitar las funciones de videollamada, por favor permite el acceso en la configuración de privacidad de tu sistema y luego reinicia la aplicación.", "openSettings": "Abrir Configuración", "cancel": "<PERSON><PERSON><PERSON>", "microphone": "Micrófono", "camera": "<PERSON><PERSON><PERSON>", "both": "Micrófono y Cámara"}, "outlookCalendar": {"title": "Calendario de Outlook", "encryptionUnavailableTitle": "Encriptación no disponible", "encryptionUnavailable": "Tu sistema operativo no admite encriptación.\nTus credenciales se guardarán en texto plano.", "field_required": "Este campo es obligatorio", "remember_credentials": "Recordar mis credenciales", "cancel": "<PERSON><PERSON><PERSON>", "submit": "In<PERSON><PERSON>"}, "supportedVersion": {"title": "Versión de espacio de trabajo no admitida"}}, "downloads": {"title": "Descargas", "notifications": {"downloadFinished": "Descarga finalizada", "downloadInterrupted": "Descarga interrumpida", "downloadCancelled": "Descarga cancelada", "downloadFailed": "<PERSON><PERSON><PERSON> fallida", "downloadExpired": "Descarga caducada", "downloadExpiredMessage": "Por favor, intenta descargarlo de nuevo desde la fuente."}, "filters": {"search": "Buscar", "server": "<PERSON><PERSON><PERSON>", "mimeType": "Tipo", "status": "Estado", "clear": "Limpiar filtros", "all": "Todos", "mimes": {"images": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "videos": "Videos", "audios": "Audios", "texts": "Textos", "files": "Archivos"}, "statuses": {"paused": "Pausada", "cancelled": "Cancelada"}}, "item": {"cancel": "<PERSON><PERSON><PERSON>", "copyLink": "<PERSON><PERSON><PERSON> enlace", "errored": "Descarga cancelada", "pause": "Pausar", "progressSize": "{{receivedBytes, byteSize}} de {{totalBytes, byteSize}} ({{ratio, percentage}})", "remove": "Eliminar de la lista", "resume": "<PERSON><PERSON><PERSON>", "retry": "Reintentar", "showInFolder": "Mostrar en la carpeta"}, "showingResults": "Mostrando resultados {{first}} - {{last}} de {{count}}"}, "certificatesManager": {"title": "Gestor de certificados", "trustedCertificates": "Certificados de confianza", "notTrustedCertificates": "Certificados no confiables", "item": {"domain": "<PERSON>inio", "actions": "Acciones", "remove": "Eliminar"}}, "settings": {"title": "Configuración", "general": "General", "certificates": "Certificados", "options": {"report": {"title": "Informar errores a los desarrolladores", "description": "Informar errores de forma anónima a los desarrolladores. La información compartida incluye el número de versión de la aplicación, el tipo de sistema operativo, la URL del servidor, el idioma del dispositivo y el tipo de error. No se comparten contenidos ni nombres de usuario.", "masDescription": "Esta opción está desactivada cuando se instala desde la Mac App Store; los errores se informarán a través del proceso de informe de errores de la Mac App Store."}, "flashFrame": {"title": "Habilitar destello de <PERSON>", "titleDarwin": "Alternar rebote del Dock en alerta", "description": "<PERSON>ce destellar la ventana para atraer la atención del usuario.", "onLinux": "Algunas distribuciones de Linux no admiten esta función.", "descriptionDarwin": "Hace rebotar el icono de la aplicación en el dock para atraer la atención del usuario."}, "hardwareAcceleration": {"title": "Aceleración de hardware", "description": "Habilita el uso de la aceleración de hardware cuando esté disponible. La aplicación se reiniciará al realizar cambios."}, "internalVideoChatWindow": {"title": "<PERSON><PERSON><PERSON> en la ventana de la aplicación", "description": "Si está activado, la videollamada se abrirá en la ventana de la aplicación en lugar del navegador predeterminado. Sin embargo, para <strong>Google Meet</strong> y <strong><PERSON><PERSON><PERSON></strong>, la grabación de pantalla no es compatible en aplicaciones Electron, por lo que siempre se abrirán en el navegador independientemente de esta configuración.", "masDescription": "Esta opción está desactivada cuando se instala desde la Mac App Store; por razones de seguridad, las videollamadas se abrirán en el navegador de forma predeterminada."}, "minimizeOnClose": {"title": "<PERSON><PERSON><PERSON> al <PERSON>er<PERSON>", "description": "Cuando se cierra la aplicación, se minimiza en lugar de cerrarse por completo. El icono de la bandeja debe estar desactivado para que esto surta efecto."}, "menubar": {"title": "Barra de menú", "description": "Mostrar la barra de menú en la parte superior de la ventana.", "disabledHint": "No se puede desactivar la barra de menú cuando la barra lateral está desactivada. La configuración sería inaccesible."}, "sidebar": {"title": "Barra lateral", "description": "Mostrar la barra lateral en el lado izquierdo de la ventana con la lista de servidores, las descargas y la configuración.", "disabledHint": "No se puede desactivar la barra lateral cuando la barra de menú está desactivada. La configuración sería inaccesible."}, "trayIcon": {"title": "Icono de la bandeja", "description": "Muestra un icono en la bandeja del sistema. Si el icono de la bandeja está activo, la aplicación se minimizará a la bandeja al cerrarla. De lo contrario, cerrará la aplicación."}, "availableBrowsers": {"title": "Nave<PERSON><PERSON>determina<PERSON>", "description": "Elija qué navegador abrirá los enlaces externos de esta aplicación. Predeterminado del Sistema usa la configuración de su sistema operativo.", "systemDefault": "Predeterminado del Sistema", "loading": "<PERSON><PERSON><PERSON>...", "current": "<PERSON>ando actualmente:"}, "clearPermittedScreenCaptureServers": {"title": "Borrar permisos de captura de pantalla", "description": "Borrar los permisos de captura de pantalla que se seleccionaron para no volver a preguntar en las videollamadas."}, "allowScreenCaptureOnVideoCalls": {"title": "Permitir captura de pantalla en videollamadas", "description": "Permitir la captura de pantalla en videollamadas. Esto solicitará permiso en cada videollamada."}, "ntlmCredentials": {"title": "Credenciales NTLM", "description": "Permitir que se utilicen las credenciales NTLM al conectarse a un servidor.", "domains": "Dominios que utilizarán las credenciales. Separados por coma. Use * para coincidir con todos los dominios."}, "videoCallWindowPersistence": {"title": "Recordar posición de la ventana de videollamada", "description": "Guardar y restaurar la posición y el tamaño de las ventanas de videollamada entre sesiones"}}}, "error": {"authNeeded": "Se necesita autenticación, prue<PERSON> con <strong>{{- auth}}</strong>", "connectTimeout": "Tiempo de conexión agotado", "differentCertificate": "El certificado es diferente al anterior.\n\n {{- detail}}", "noValidServerFound": "No se encontró ningún servidor válido en la URL", "offline": "¡Verifica tu conexión a Internet!"}, "landing": {"invalidUrl": "URL no válida", "validating": "Validando...", "inputUrl": "Ingresa la URL de tu servidor", "connect": "Conectar"}, "menus": {"about": "Acerca de {{- appName}}", "addNewServer": "Agregar &nuevo servidor", "back": "&Atrás", "clearTrustedCertificates": "Borrar certificados de confianza", "close": "<PERSON><PERSON><PERSON>", "copy": "&Copiar", "cut": "Cor&tar", "developerMode": "<PERSON><PERSON> desarrollador", "disableGpu": "Deshabilitar GPU", "documentation": "Documentación", "downloads": "Descargas", "settings": "Configuración", "editMenu": "&Editar", "fileMenu": "&Archivo", "forward": "&Avanzar", "helpMenu": "&<PERSON><PERSON><PERSON>", "hide": "Ocultar {{- appName}}", "hideOthers": "Ocultar otros", "learnMore": "Más información", "minimize": "<PERSON><PERSON><PERSON>", "openDevTools": "Abrir &Herramientas de desarrollo", "openDevToolsOnAllWindows": "Abrir &Herramientas de desarrollo en todas las ventanas", "paste": "&<PERSON><PERSON><PERSON>", "quit": "&<PERSON><PERSON> de {{- appName}}", "redo": "&<PERSON>hacer", "reload": "&Recargar", "reportIssue": "Informar problema", "resetAppData": "Restablecer datos de la aplicación", "resetZoom": "Restablecer zoom", "selectAll": "Seleccionar &todo", "services": "<PERSON><PERSON><PERSON>", "showFullScreen": "Pantalla completa", "showMenuBar": "Barra de menú", "showOnUnreadMessage": "Mostrar en mensajes no leídos", "showServerList": "Lista de servidores", "showTrayIcon": "Icono en la bandeja", "toggleDevTools": "Alternar &Herramientas de desarrollo", "undo": "&Deshacer", "unhide": "<PERSON><PERSON> todos", "viewMenu": "&Ver", "windowMenu": "&Ventana", "zoomIn": "Acercar", "zoomOut": "<PERSON><PERSON><PERSON>"}, "loadingError": {"title": "Fallo al cargar el servidor", "announcement": "Houston, tenemos un problema", "reload": "Recargar"}, "videoCall": {"loading": {"initial": "<PERSON><PERSON><PERSON>...", "reloading": "Recargan<PERSON>...", "description": "Por favor espera mientras nos conectamos a la videollamada"}, "error": {"title": "Error al cargar la videollamada", "announcement": "Houston, tenemos un problema", "timeout": "Tiempo de espera agotado - la videollamada no pudo cargar en 15 segundos", "crashed": "Webview se ha bloqueado", "maxRetriesReached": "Error al cargar después de múltiples intentos", "reload": "<PERSON><PERSON><PERSON>"}}, "unsupportedServer": {"title": "{{instanceDomain}} está ejecutando una versión no compatible de Rocket.Chat", "announcement": "Un administrador debe actualizar el espacio de trabajo a una versión compatible para habilitar el acceso desde aplicaciones móviles y de escritorio.", "moreInformation": "Más información"}, "selfxss": {"title": "¡Alto!", "description": "Esta es una función del navegador destinada a los desarrolladores. Si alguien te pidió que copies y pegues algo aquí para habilitar una función de Rocket.Chat o \"hackear\" la cuenta de alguien, es un fraude y les dará acceso a tu cuenta de Rocket.Chat.", "moreInfo": "Ver https://go.rocket.chat/i/xss para obtener más información."}, "sidebar": {"addNewServer": "Agregar nuevo servidor", "downloads": "Descargas", "settings": "Configuración", "item": {"reload": "<PERSON><PERSON><PERSON> servidor", "remove": "Eliminar servidor", "openDevTools": "<PERSON><PERSON><PERSON> desarrollo", "clearCache": "<PERSON><PERSON><PERSON> caché", "clearStorageData": "Bo<PERSON>r datos de almacenamiento", "copyCurrentUrl": "Copiar URL actual"}}, "touchBar": {"formatting": "Formato", "selectServer": "<PERSON><PERSON><PERSON><PERSON><PERSON> servidor"}, "tray": {"menu": {"show": "Mostrar", "hide": "Ocultar", "quit": "Salir"}, "tooltip": {"noUnreadMessage": "{{- appName}}: no hay mensajes sin leer", "unreadMention": "{{- appName}}: tienes una mención/mensaje directo sin leer", "unreadMention_plural": "{{- appName}}: tienes {{- count}} menciones/mensajes directos sin leer", "unreadMessage": "{{- appName}}: tienes mensajes sin leer"}, "balloon": {"stillRunning": {"title": "{{- appName}} todavía está en ejecución", "content": "{{- appName}} está configurado para permanecer en ejecución en la bandeja del sistema/área de notificación."}}}, "taskbar": {"unreadMessage": "Mensajes no leídos", "unreadMention": "Menciones no leídas", "noUnreadMessage": "No hay mensajes no leídos"}, "screenSharing": {"permissionDenied": "Permiso de Grabación de Pantalla Denegado", "permissionRequired": "Se requiere permiso de grabación de pantalla para compartir tu pantalla.", "permissionInstructions": "Por favor, actívalo en las preferencias del sistema e inténtalo de nuevo.", "title": "Compartir tu pantalla", "entireScreen": "Tu pantalla completa", "applicationWindow": "Ventana de aplicación", "noScreensFound": "No se encontraron pantallas", "noWindowsFound": "No se encontraron ventanas", "cancel": "<PERSON><PERSON><PERSON>", "share": "Compartir"}}