{"contextMenu": {"cut": "&Wytnij", "copy": "&<PERSON><PERSON><PERSON><PERSON>", "paste": "Wk&lej", "selectAll": "Z&aznacz wszystko", "undo": "&Cofnij", "redo": "&Ponów", "spellingLanguages": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> p<PERSON>i", "moreSpellingSuggestions": "Więcej propozycji pisowni", "noSpellingSuggestions": "<PERSON><PERSON> sugestii", "copyLinkAddress": "<PERSON><PERSON><PERSON><PERSON> ad<PERSON> odn<PERSON>ś<PERSON>", "copyLinkText": "Kopiuj tekst odnośnika", "openLink": "Otw<PERSON>rz <PERSON>", "saveImageAs": "Zapisz obraz jako..."}, "dialog": {"about": {"title": "O {{- appName}}", "version": "Wersja: <1>{{-version}}</1>", "checkUpdates": "Sprawdź aktualizacje", "checkUpdatesOnStart": "Sprawdzanie aktualizacji przy uruchomieniu", "noUpdatesAvailable": "Brak dostępnych aktualizacji.", "copyright": "<PERSON><PERSON>a autorskie {{copyright}}", "errorWhenLookingForUpdates": "Wystą<PERSON>ł błąd podczas wyszukiwania aktualizacji", "updateChannel": {"label": "Kanał aktualizacji", "latest": "Stabilny", "beta": "Beta", "alpha": "Alfa (Eksperymentalny)"}}, "addServer": {"title": "<PERSON><PERSON><PERSON>", "message": "<PERSON><PERSON><PERSON> do<PERSON> \"{{- host}}\" do swojej listy serwerów?", "add": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>"}, "addServerError": {"title": "Niepoprawny host", "message": "<PERSON><PERSON> \"{{- host}}\" nie mógł być zwery<PERSON>y, więc nie został dodany."}, "certificateError": {"title": "<PERSON><PERSON><PERSON><PERSON>", "message": "<PERSON><PERSON><PERSON> certyfikatowi od \"{{- issuerName}}\"?", "yes": "Tak", "no": "<PERSON><PERSON>"}, "resetAppData": {"title": "<PERSON>set danych a<PERSON>", "message": "Spowoduje to wylogowanie ze wszystkich zespołów i przywrócenie pierwotnych ustawień aplikacji. Tego nie da się cofnąć.", "yes": "Tak", "cancel": "<PERSON><PERSON><PERSON>"}, "screenshare": {"title": "Udostępnij swój ekran", "announcement": "Wybierz ekran do udostępnienia"}, "update": {"title": "Dostępna aktualizacja", "announcement": "Nowa aktualizacja jest dostępna", "message": "Nowa wersja aplikacji Rocket.Chat Desktop jest dostępna!", "currentVersion": "Bieżąca wersja:", "newVersion": "Nowa wersja:", "install": "Zainstaluj aktualizację", "remindLater": "Przypomnij mi później", "skip": "Pomiń tę wersję"}, "updateDownloading": {"title": "Pobieranie aktualizacji", "message": "Zostaniesz poinformowany gdy aktualizacja będzie gotowa do zainstalowania", "ok": "OK"}, "updateInstallLater": {"title": "Instalowanie później", "message": "Aktualizacja zostanie zainstalowana kiedy wyjdziesz z programu", "ok": "OK"}, "updateReady": {"title": "Aktualizacja gotowa do instalacji", "message": "Aktualizacja została pobrana", "installNow": "<PERSON><PERSON><PERSON><PERSON><PERSON> teraz", "installLater": "Zainstaluj później"}, "updateSkip": {"title": "Pomiń aktualizację", "message": "Powiadomimy Cię gdy będzie dostępna następna aktualizacja\nJeśli zmienisz zdanie możesz sprawdzić aktualizacje z menu O programie.", "ok": "OK"}, "selectClientCertificate": {"announcement": "<PERSON><PERSON><PERSON><PERSON>", "select": "<PERSON><PERSON><PERSON><PERSON>", "validDates": "Ważny od {{-validStart,}} do {{-validExpiry,}}"}, "mediaPermission": {"title": "Wymagane Uprawnienie Mediów", "message": "Dostęp do {{- permissionType}} jest obecnie wyłączony w ustawieniach systemu.", "detail": "<PERSON><PERSON> w<PERSON><PERSON><PERSON><PERSON><PERSON> funkcje rozmów wideo, <PERSON><PERSON><PERSON> z<PERSON> na dostęp w ustawieniach prywatności systemu, a następnie uruchomić ponownie aplikację.", "openSettings": "Ot<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "microphone": "Mikrofon", "camera": "<PERSON><PERSON><PERSON>", "both": "Mikrofon i Kamera"}}, "settings": {"title": "Ustawienia", "general": "Ogólne", "options": {"trayIcon": {"title": "<PERSON><PERSON><PERSON>", "description": "Wyświetla ikonę w zasobniku systemowym. Gdy ikona zasobnika jest aktywna, aplikacja będzie minimalizowana do zasobnika przy zamykaniu. W przeciwnym razie aplikacja zostanie zamknięta."}, "availableBrowsers": {"title": "Domyślna przeglądarka", "description": "<PERSON><PERSON><PERSON><PERSON>, która przeglądarka będzie otwierać linki zewnętrzne z tej aplikacji. Ustawienie domyślne systemu używa ustawień Twojego systemu operacyjnego.", "systemDefault": "Domyślna systemu", "loading": "Ładowanie przeglądarek...", "current": "Aktualnie używana:"}}}, "error": {"authNeeded": "<PERSON><PERSON><PERSON><PERSON>, spr<PERSON><PERSON><PERSON> <strong>{{- auth}}</strong>", "connectTimeout": "Przekroczony czas oczekiwania podczas próby połączenia", "differentCertificate": "Certyfikat jest inny od poprzedniego.\n\n {{- detail}}", "noValidServerFound": "Nie znaleziono prawidłowego serwera pod podanym URL", "offline": "Sprawdź połączenie z Internetem!"}, "landing": {"invalidUrl": "Nieprawidłowy url", "validating": "Sprawdzanie...", "inputUrl": "Wprowadź adres swojego serwera", "connect": "Połącz"}, "menus": {"about": "O programie {{- appName}}", "addNewServer": "Dodaj &nowy serwer", "back": "W&stecz", "clearTrustedCertificates": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> zaufane c<PERSON>y", "close": "Zamknij", "copy": "&<PERSON><PERSON><PERSON><PERSON>", "cut": "&Wytnij", "developerMode": "Tryb programisty", "documentation": "Dokumentacja", "editMenu": "&<PERSON><PERSON><PERSON><PERSON>", "fileMenu": "&Plik", "forward": "&Do przodu", "helpMenu": "Po&moc", "hide": "<PERSON><PERSON><PERSON><PERSON> {{- appName}}", "hideOthers": "<PERSON>k<PERSON>j <PERSON>ł<PERSON>", "learnMore": "Dowiedz się więcej", "minimize": "Minimalizacja", "openDevTools": "Otwórz &Narzędzia deweloperskie", "paste": "Wk&lej", "quit": "Wy&jdź {{- appName}}", "redo": "P&onów", "reload": "P&rzeładuj", "reportIssue": "Raportowanie błędu", "resetAppData": "<PERSON>set danych a<PERSON>", "resetZoom": "Reset zoomu", "selectAll": "&Zaznacz wszystko", "services": "Usługi", "showFullScreen": "Pełny ekran", "showMenuBar": "Pasek menu", "showOnUnreadMessage": "Pokaż nieprzeczytane wiadomości", "showServerList": "Lista serwerów", "showTrayIcon": "<PERSON><PERSON><PERSON>", "toggleDevTools": "Przełącz &Narzędzia deweloperskie", "undo": "&Cofnij", "unhide": "Pokaż wszystko", "viewMenu": "&Widok", "windowMenu": "&Okno", "zoomIn": "<PERSON><PERSON><PERSON><PERSON><PERSON>ż", "zoomOut": "<PERSON><PERSON>"}, "loadingError": {"title": "Błąd ładowania serwera", "announcement": "<PERSON><PERSON><PERSON>, mamy problem", "reload": "Przeładuj"}, "videoCall": {"loading": {"initial": "Ładowanie połączenia wideo...", "reloading": "Ponowne ładowanie połączenia wideo...", "description": "<PERSON><PERSON><PERSON>, łączymy z połączeniem wideo"}, "error": {"title": "Nie udało się załadować połączenia wideo", "announcement": "Houston, mamy problem", "timeout": "Przekroczono czas oczekiwania - nie udało się załadować połączenia wideo w ciągu 15 sekund", "crashed": "Webview się zawiesił", "maxRetriesReached": "<PERSON>e udało się załadować po wielu próbach", "reload": "Przeładuj połączenie wideo"}}, "selfxss": {"title": "Stop!", "description": "To jest funk<PERSON>ja przeglądarki dedykowana programistom. <PERSON><PERSON><PERSON> k<PERSON>ś powiedział Ci, by <PERSON><PERSON><PERSON><PERSON><PERSON> i w<PERSON><PERSON> coś tutaj aby włączyć funcjonalność Rocket.<PERSON><PERSON> lub \"<PERSON><PERSON><PERSON><PERSON>\" c<PERSON><PERSON><PERSON> konto, to jest to oszustwo a Ty dasz temu komuś dostęp do swojego konta Rocket.Chat.", "moreInfo": "Wejdź tutaj https://go.rocket.chat/i/xss po więcej informacji."}, "sidebar": {"addNewServer": "<PERSON><PERSON><PERSON> nowy serwer", "item": {"reload": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> serwer", "remove": "<PERSON><PERSON><PERSON> serwer", "openDevTools": "Otwórz Narzędzia deweloperskie"}}, "touchBar": {"formatting": "Formatowanie", "selectServer": "<PERSON><PERSON><PERSON><PERSON> serwer"}, "tray": {"menu": {"show": "Po<PERSON><PERSON>", "hide": "<PERSON><PERSON><PERSON><PERSON>", "quit": "Wyjdź"}, "tooltip": {"noUnreadMessage": "{{- appName}}: <PERSON><PERSON> w<PERSON>dom<PERSON>", "unreadMention": "{{- appName}}: <PERSON><PERSON> wzmiankę/w<PERSON><PERSON><PERSON><PERSON><PERSON>", "unreadMention_plural": "{{- appName}}: <PERSON><PERSON> {{- count}} ni<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> wiadomości/wzmianek", "unreadMessage": "{{- appName}}: <PERSON><PERSON> w<PERSON>"}, "balloon": {"stillRunning": {"title": "{{- appName}} w<PERSON><PERSON><PERSON>ła", "content": "{{- appName }} jest ustawiony aby działał w zasobniku systemowym/obszarze powiadomień."}}}, "taskbar": {"unreadMessage": "Nieprzeczytane wiadomości", "unreadMention": "Nieprzeczytane wzmianki", "noUnreadMessage": "<PERSON><PERSON> nieprz<PERSON><PERSON><PERSON>ch wiadomości"}, "screenSharing": {"permissionDenied": "Odmowa uprawnień do nagrywania ekranu", "permissionRequired": "Uprawnienie do nagrywania ekranu jest wymagane, aby u<PERSON><PERSON><PERSON><PERSON> ekran.", "permissionInstructions": "Włącz je w preferencjach systemowych i spróbuj ponownie.", "title": "Udostępnij swój ekran", "entireScreen": "Cały ekran", "applicationWindow": "Okno aplikacji", "noScreensFound": "Nie znaleziono ekranów", "noWindowsFound": "Nie znaleziono okien", "cancel": "<PERSON><PERSON><PERSON>", "share": "Udostępnij"}}