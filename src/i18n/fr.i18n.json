{"contextMenu": {"cut": "Cou&per", "copy": "&Copier", "paste": "&Coller", "selectAll": "&<PERSON><PERSON> s<PERSON>ner", "undo": "&Annuler", "redo": "&Rétablir", "spelling": "Orthographe", "spellingLanguages": "Langues d'orthographe", "moreSpellingSuggestions": "Plus de suggestions d'orthographe", "noSpellingSuggestions": "<PERSON><PERSON><PERSON> suggestions", "copyLinkAddress": "Copier le lien", "copyLinkText": "<PERSON><PERSON>r le texte du lien", "openLink": "<PERSON>uv<PERSON>r le lien", "saveImageAs": "Enregistrer l'image sous...", "copyImage": "Copier l'image"}, "dialog": {"about": {"title": "À propos de {{- appName}}", "version": "Version : <1>{{-version}}</1>", "checkUpdates": "Vérifier les mises à jour", "checkUpdatesOnStart": "Vérifier les mises à jour au démarrage", "noUpdatesAvailable": "Aucune mise à jour n'est disponible.", "copyright": "Copyright {{copyright}}", "errorWhenLookingForUpdates": "Une erreur s'est produite lors de la recherche de mises à jour", "updateChannel": {"label": "Canal de mise à jour", "latest": "Stable", "beta": "<PERSON><PERSON><PERSON>", "alpha": "Alpha (Expérimental)"}}, "addServer": {"title": "Ajouter un serveur", "message": "Vous voulez ajouter \"{{- host}}\" à votre liste de serveurs ?", "add": "Ajouter", "cancel": "Annuler"}, "addServerError": {"title": "<PERSON><PERSON><PERSON> invalide", "message": "<PERSON>'hôte \"{{- host}}\" n'a pas pu être validé, donc n'a pas été ajouté."}, "certificateError": {"title": "Erreur de certificat", "message": "Faites-vous confiance au certificat de \"{{- issuerName}}\" ?", "yes": "O<PERSON>", "no": "Non"}, "downloadRemoval": {"title": "Êtes-vous sûr ?", "message": "Supprimer ce téléchargement ?", "yes": "O<PERSON>", "no": "Non"}, "resetAppData": {"title": "Réinitialiser les données de l'application", "message": "Cela vous déconnectera de toutes vos équipes et réinitialisera l'application à ses paramètres d'origine. Ceci ne peut pas être annulé.", "yes": "O<PERSON>", "cancel": "Annuler"}, "clearPermittedScreenCaptureServers": {"title": "Effacer les serveurs de capture d'écran autorisés", "message": "<PERSON><PERSON> effacera toutes les autorisations pour les serveurs de capture d'écran, les obligeant à redemander l'autorisation. Ça ne peut pas être annulé.", "yes": "O<PERSON>", "cancel": "Annuler"}, "screenshare": {"title": "Partagez votre écran", "announcement": "Sélectionnez un écran à partager"}, "update": {"title": "Une mise à jour est disponible", "announcement": "Une nouvelle mise à jour est disponible", "message": "Une mise à jour de Rocket.Chat Desktop App est disponible !", "currentVersion": "Version actuelle :", "newVersion": "Nouvelle version :", "install": "Installer la mise à jour", "remindLater": "Me rappeler plus tard", "skip": "Ignorer cette version"}, "updateDownloading": {"title": "Téléchargement de la mise à jour", "message": "Vous serez averti quand la mise à jour sera prête à être installée", "ok": "OK"}, "updateInstallLater": {"title": "L'installation se fera plus tard", "message": "La mise à jour sera installé à la fermeture de l'application", "ok": "OK"}, "updateReady": {"title": "La mise à jour est prête a être installée", "message": "La mise à jour a été téléchargée", "installNow": "Installer maintenant", "installLater": "Installer plus tard"}, "updateSkip": {"title": "Ignorer la mise à jour", "message": "Vous serez averti quand la prochaine mise à jour sera disponible\nSi vous changez d'avis, vous pouvez vérifier les mises à jour dans le menu A propos.", "ok": "OK"}, "selectClientCertificate": {"announcement": "Sélectionnez un certificat", "select": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "validDates": "Valable du {{-validStart,}} au {{-validExpiry,}}"}, "openingExternalProtocol": {"title": "Lien avec un protocole personnalisé", "message": "Lien {{- protocol }} nécessite une application externe.", "detail": "Le lien demandé est {{- url }}. Voulez-vous continuer ?", "dontAskAgain": "Ouvrez toujours ces types de liens dans l'application associée", "yes": "O<PERSON>", "no": "Non"}, "allowVideoCallCaptureScreen": {"title": "L'appel vidéo essaie de capturer votre écran", "message": "L'appel vidéo demande la permission de capturer votre écran.", "detail": "Le serveur d'appels vidéo {{- url }} nécessite une autorisation pour partager votre écran avec d'autres personnes.", "dontAskAgain": "Toujours autoriser les appels vidéo de ce serveur pour capturer votre écran", "yes": "Autoriser", "no": "Annuler"}, "mediaPermission": {"title": "Autorisation Multimédia Requise", "message": "L'accès au {{- permissionType}} est actuellement désactivé dans les paramètres de votre système.", "detail": "Pour activer les fonctionnalités d'appel vidéo, veuil<PERSON>z autoriser l'accès dans les paramètres de confidentialité de votre système, puis redémarrer l'application.", "openSettings": "<PERSON><PERSON><PERSON><PERSON>r les Paramètres", "cancel": "Annuler", "microphone": "Microphone", "camera": "Caméra", "both": "Microphone et Caméra"}}, "downloads": {"title": "Téléchargements", "notifications": {"downloadFinished": "Téléchargement terminé", "downloadInterrupted": "Téléchargement interrompu", "downloadCancelled": "Téléchargement annulé", "downloadFailed": "Echec du téléchargement", "downloadExpired": "Téléchargement expiré", "downloadExpiredMessage": "Veuillez réessayer le téléchargement à partir de la source."}, "filters": {"search": "<PERSON><PERSON><PERSON>", "server": "Ser<PERSON><PERSON>", "mimeType": "Type", "status": "Statut", "clear": "Effacer les filtres", "all": "<PERSON>ut", "mimes": {"images": "Images", "videos": "Vid<PERSON><PERSON>", "audios": "Audios", "texts": "Textes", "files": "Fichiers"}, "statuses": {"paused": "Mis en pause", "cancelled": "<PERSON><PERSON><PERSON>"}}, "item": {"cancel": "Annuler", "copyLink": "Copier le lien", "errored": "Téléchargement annulé", "pause": "Pause", "progressSize": "{{receivedBytes, byteSize}} de {{totalBytes, byteSize}} ({{ratio, percentage}})", "remove": "<PERSON><PERSON><PERSON> de la liste", "resume": "Reprendre", "retry": "<PERSON><PERSON><PERSON><PERSON>", "showInFolder": "Afficher dans le dossier"}, "showingResults": "Affichage des résultats {{first}} - {{last}} de {{count}}"}, "certificatesManager": {"title": "Gestionnaire de certificats", "trustedCertificates": "Certificats de confiance", "notTrustedCertificates": "Certificats non fiables", "item": {"domain": "Domaine", "actions": "Actions", "remove": "<PERSON><PERSON><PERSON><PERSON>"}}, "settings": {"title": "Paramètres", "general": "Général", "certificates": "Certificats", "options": {"report": {"title": "Signaler les erreurs aux développeurs", "description": "Signaler les erreurs de manière anonyme aux développeurs. Les informations partagées incluent le numéro de version de l'application, le type de système d'exploitation, l'URL du serveur, la langue de l'appareil et le type d'erreur. Aucun contenu ou nom d'utilisateur n'est partagé.", "masDescription": "Cette option est désactivée lorsqu'elle est installée à partir du Mac App Store, les erreurs seront signalisées via le processus de rapport d'erreur du Mac App Store."}, "flashFrame": {"title": "Activer le cadre Flash", "titleDarwin": "Basculer le Dock Bounce en cas d'alerte", "description": "Clignote la fenêtre pour attirer l'attention de l'utilisateur.", "onLinux": "Certaines distributions Linux ne prennent pas en charge cette fonctionnalité.", "descriptionDarwin": "Fait rebondir l'icône de l'application dans le dock pour attirer l'attention de l'utilisateur."}, "hardwareAcceleration": {"title": "Accélération matérielle", "description": "Active l'utilisation de l'accélération matérielle lorsqu'elle est disponible. L'application se rechargera en cas de changement."}, "internalVideoChatWindow": {"title": "<PERSON><PERSON><PERSON><PERSON><PERSON> le chat vidéo à l'aide de la fenêtre de l'application", "description": "Si cette option est activée, le chat vidéo s'ouvrira dans la fenêtre de l'application au lieu du navigateur par défaut. Cependant, pour <strong>Google Meet</strong> et <strong><PERSON><PERSON><PERSON></strong>, l'enregistrement d'écran n'est pas pris en charge dans les applications Electron, donc ils s'ouvriront toujours dans le navigateur, quel que soit ce paramètre.", "masDescription": "Cette option est désactivée lorsqu'elle est installée à partir du Mac App Store, pour des raisons de sécurité, elle ouvrira le chat vidéo en utilisant le navigateur par défaut."}, "minimizeOnClose": {"title": "Minimiser à la fermeture", "description": "Une fois fermée, l'application sera minimisée, sinon elle quittera l'application. L'icône de la barre d'état doit être désactivée pour que cela prenne effet."}, "menubar": {"title": "Barre de menu", "description": "Afficher la barre de menus en haut de la fenêtre.", "disabledHint": "Impossible de désactiver la barre de menu quand la barre latérale est désactivée. Les paramètres deviendraient inaccessibles."}, "sidebar": {"title": "Barre la<PERSON>", "description": "Afficher la barre latérale à gauche de la fenêtre avec la liste des serveurs, les téléchargements et les paramètres.", "disabledHint": "Impossible de désactiver la barre latérale quand la barre de menu est désactivée. Les paramètres deviendraient inaccessibles."}, "trayIcon": {"title": "Icône de la barre d'état système", "description": "Afficher l'icône dans la barre d'état système. Si l'icône est active, l'application sera masquée dans la barre d'état lors de la fermeture. Sinon, elle sera complètement fermée."}, "availableBrowsers": {"title": "Navigateur par défaut", "description": "Choisissez quel navigateur ouvrira les liens externes de cette application. La valeur par défaut du système utilise les paramètres de votre système d'exploitation.", "systemDefault": "Par défaut du système", "loading": "Chargement des navigateurs...", "current": "Actuellement utilisé:"}, "clearPermittedScreenCaptureServers": {"title": "Effacer les autorisations de capture d'écran", "description": "Effacez les autorisations de capture d'écran qui ont été sélectionnées pour ne plus demander lors des appels vidéo."}}}, "error": {"authNeeded": "Authentification requise, essayez <strong>{{- auth}}</strong>", "connectTimeout": "<PERSON><PERSON><PERSON> d'attente lors de la connexion", "differentCertificate": "Le certificat est different du dernier utilisé.\n\n {{- detail}}", "noValidServerFound": "Aucun serveur valide trouvé à cette adresse", "offline": "Vérifiez votre connexion Internet !"}, "landing": {"invalidUrl": "URL invalide", "validating": "Validation...", "inputUrl": "Ajouter l'URL du serveur", "connect": "Connexion"}, "menus": {"about": "À propos de {{- appName}}", "addNewServer": "Ajouter un &nouveau serveur", "back": "&Retour", "clearTrustedCertificates": "Effacer les certificats de confiance", "close": "<PERSON><PERSON><PERSON>", "copy": "&Copier", "cut": "Cou&per", "developerMode": "Mode développeur", "disableGpu": "Désactiver le GPU", "documentation": "Documentation", "downloads": "Téléchargements", "settings": "Paramètres", "editMenu": "&Éditer", "fileMenu": "&Fichier", "forward": "&Suivant", "helpMenu": "&Aide", "hide": "Masquer {{- appName}}", "hideOthers": "Masquer les autres", "learnMore": "En savoir plus", "minimize": "<PERSON><PERSON><PERSON><PERSON>", "openDevTools": "Ouvrir &DevTools", "openDevToolsOnAllWindows": "Ouvrir &DevTools sur toutes les fenêtres", "paste": "&Coller", "quit": "&<PERSON><PERSON>ter {{- appName}}", "redo": "&Rétablir", "reload": "Recharger", "reportIssue": "Signaler un problème", "resetAppData": "Effacer les données de l'application", "resetZoom": "Réinitialiser le zoom", "selectAll": "<PERSON><PERSON><PERSON><PERSON><PERSON> tout", "services": "Services", "showFullScreen": "Plein écran", "showMenuBar": "Barre des menus", "showOnUnreadMessage": "Afficher la fenêtre quand il y a des messages non lus", "showServerList": "Liste des serveurs", "showTrayIcon": "Icône dans la barre des tâches", "toggleDevTools": "Afficher &DevTools", "undo": "&Annuler", "unhide": "<PERSON><PERSON><PERSON><PERSON> tout", "viewMenu": "&Voir", "windowMenu": "&Fenêtre", "zoomIn": "<PERSON>mer", "zoomOut": "Zoom arri<PERSON>"}, "loadingError": {"title": "Echec du chargement du serveur", "announcement": "Houston, nous avons un problème", "reload": "Recharger"}, "videoCall": {"loading": {"initial": "Chargement de l'appel vidéo...", "reloading": "Rechargement de l'appel vidéo...", "description": "Veuillez patienter pendant que nous nous connectons à l'appel vidéo"}, "error": {"title": "Échec du chargement de l'appel vidéo", "announcement": "Houston, nous avons un problème", "timeout": "<PERSON><PERSON><PERSON> d'attente dépassé - l'appel vidéo n'a pas pu se charger en 15 secondes", "crashed": "Webview a planté", "maxRetriesReached": "Échec du chargement après plusieurs tentatives", "reload": "Recharger l'appel vidéo"}}, "selfxss": {"title": "Stop !", "description": "Il s'agit d'une fonctionnalité de navigateur destinée aux développeurs. Si quelqu'un vous a dit de copier-coller quelque chose ici pour activer une fonctionnalité Rocket.Chat ou \"pirater\" le compte de quelqu'un, il s'agit d'une arnaque et ça lui donnera accès à votre compte Rocket.Chat.", "moreInfo": "Voir https://go.rocket.chat/i/xss pour plus d'informations."}, "sidebar": {"addNewServer": "Ajouter un nouveau serveur", "downloads": "Téléchargements", "settings": "Paramètres", "item": {"reload": "Recharger le serveur", "remove": "<PERSON><PERSON><PERSON> le serveur", "openDevTools": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "clearCache": "Vider le cache", "clearStorageData": "Effacer les données de stockage"}}, "touchBar": {"formatting": "Mise en page", "selectServer": "Sélectionnez un serveur"}, "tray": {"menu": {"show": "<PERSON><PERSON><PERSON><PERSON>", "hide": "<PERSON><PERSON>", "quit": "<PERSON><PERSON><PERSON>"}, "tooltip": {"noUnreadMessage": "{{- appName}}: aucun message non lu", "unreadMention": "{{- appName}}: vous avez une mention / un message privé non lu", "unreadMention_plural": "{{- appName}}: vous avez {{- count}} mentions / messages privés non lus", "unreadMessage": "{{- appName}}: vous avez des messages non lus"}, "balloon": {"stillRunning": {"title": "{{- appName}} est toujours en cours d'exécution", "content": "{{- appName }} est configuré pour continuer à fonctionner dans la barre d'état système / zone de notification."}}}, "taskbar": {"unreadMessage": "Messages non lus", "unreadMention": "Mentions non lues", "noUnreadMessage": "Pas de messages non lus"}, "screenSharing": {"permissionDenied": "Permission d'Enregistrement d'Écran Refusée", "permissionRequired": "La permission d'enregistrement d'écran est requise pour partager votre écran.", "permissionInstructions": "Veuillez l'activer dans les préférences système et réessayer.", "title": "Partager votre écran", "entireScreen": "<PERSON><PERSON><PERSON> é<PERSON>ran <PERSON>tier", "applicationWindow": "Fenêtre d'application", "noScreensFound": "Aucun é<PERSON>ran trouvé", "noWindowsFound": "<PERSON><PERSON><PERSON> fenêtre trouvée", "cancel": "Annuler", "share": "Partager"}}