# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Build and Development Commands

### Essential Commands
- `yarn` - Install dependencies
- `yarn start` - Build and run the app in development mode with hot reload
- `yarn build` - Build the app for production
- `yarn build:watch` - Build with automatic rebuilding on file changes
- `yarn test` - Run Jest tests with Electron runner
- `yarn lint` - Run ESLint and TypeScript checks
- `yarn lint-fix` - Fix ESLint issues automatically  
- `yarn release` - Package the app for distribution

### Platform-specific Build Commands
- `yarn build-mac` - Build for macOS (universal binary)
- `yarn build-mas` - Build for Mac App Store
- `yarn build-win` - Build for Windows
- `yarn build-linux` - Build for Linux

## Architecture Overview

This is an Electron desktop application for Rocket.Chat built with TypeScript and React.

### Entry Points
The application has three main entry files compiled by Rollup:
- `src/main.ts` - Main Electron process that orchestrates the application
- `src/rootWindow.ts` - Renderer process for the main window UI
- `src/preload.ts` - Preload script with privileged API access bridging main and renderer processes

### Core Technologies
- **Electron 37.2.4** - Desktop application framework
- **TypeScript 5.7.3** - Type-safe JavaScript
- **React 18.3.1** - UI components
- **Redux 5.0.1** - State management
- **Rollup** - Build bundler
- **Jest with Electron runner** - Testing framework

### Key Directories
- `src/main/` - Main process code
- `src/ui/` - React components and UI logic
- `src/preload/` - Preload scripts for secure IPC
- `src/servers/` - Server connection management
- `src/downloads/` - Download handling
- `src/notifications/` - Notification system
- `src/videoCallWindow/` - Video call window management
- `src/store/` - Redux store configuration
- `src/i18n/` - Internationalization resources

### State Management
The app uses Redux with a modular reducer structure. State is synchronized between main and renderer processes via IPC channels defined in `src/ipc/`.

### Server Configuration
Default servers can be configured via `servers.json` at the root level or in user preferences folders. The app supports multiple Rocket.Chat server connections simultaneously.

### Testing Strategy
Tests are split between main process tests (`*.main.spec.ts`) and renderer process tests (`*.spec.ts`). The test runner uses `@kayahr/jest-electron-runner` for proper Electron environment simulation.

## Important Configuration Files

- `rollup.config.mjs` - Build configuration
- `electron-builder.json` - Electron packager configuration  
- `tsconfig.json` - TypeScript compiler options
- `.eslintrc.json` - ESLint rules extending `@rocket.chat/eslint-config`
- `jest.config.js` - Jest test configuration with separate projects for main/renderer

## Code Style and Conventions

- Follow existing TypeScript patterns with strict mode enabled
- React components use functional components with hooks
- Redux actions follow FSA (Flux Standard Action) pattern
- File naming: camelCase for files, PascalCase for components
- All new code must pass ESLint and TypeScript checks
- Prefer editing existing files over creating new ones
- **No unnecessary comments** - Code must be self-documenting through clear naming and structure
- Only add comments for complex business logic or non-obvious decisions
- Keep code clean and professional - This is an open source project

## Library and Framework Usage

- **Always verify before using** - Check official documentation and type definitions, never guess
- For TypeScript libraries: Check `.d.ts` files in `node_modules/@package-name/dist/`
- For React components: Verify prop types, interfaces, and valid values in type definitions
- Never assume prop values, tokens, or API endpoints work without verification
- This prevents errors and ensures proper library usage

## UI Development - Fuselage Design System

- **MANDATORY: Use Fuselage components** for all UI development
- Storybook documentation: https://rocketchat.github.io/fuselage
- Repository: https://github.com/RocketChat/fuselage
- **Only create custom components when Fuselage doesn't provide the needed component**
- For Fuselage components: Check `Theme.d.ts` for valid color tokens and values
- Always use Fuselage's Box, Button, TextInput, Modal, etc. instead of HTML elements
- Follow Fuselage patterns for spacing, colors, and typography
- Import from `@rocket.chat/fuselage` package
- **Reference implementation examples**: 
  - Check https://github.com/RocketChat/Rocket.Chat for real-world Fuselage usage patterns
  - If available locally, reference the main Rocket.Chat repository for implementation examples

## Documentation

- **Check existing docs first** - Always look in `docs/` folder before working on features
- **Update existing documentation** when making changes to documented features
- **Create documentation** for new features including flow diagrams, architecture explanations, usage examples
- Place all documentation in `docs/` directory with descriptive filenames
- Use markdown format for all documentation
- **Use simple language** - Write as if explaining to a colleague in plain English
- Avoid complex words: use "advanced" not "sophisticated", "use" not "utilize", "complex" not "intricate"

## Communication Guidelines

- Avoid subjective quality descriptors like "smart", "intelligent", "excellent", "dumb", or "poor"
- Don't call existing code "slow" - only state something is "faster" when measurably true
- Use measurable improvements: "reduced memory usage", "improved performance by X%", "simplified"
- Respect existing code - Don't diminish previous work; focus on objective improvements
- Describe changes factually without rating solutions as superior or inferior
- **Stay neutral** - We implement and describe, not evaluate. Let users judge the value
- Remember that all code is temporary - Today's solution will be tomorrow's legacy code
- Focus on "what" and "how", not subjective "better" or "worse"
- When changes provide benefits, describe them objectively (e.g., "reduces API calls from 5 to 1" not "better approach")
- **Keep PR descriptions simple** - Use straightforward language, avoid fancy or overly professional words
- Write clearly and directly as if explaining to a colleague

## Git Guidelines

- **NEVER** make git commits, merges, rebases, or any git write operations unless explicitly requested by the user
- **NEVER** push to remote repositories unless explicitly requested
- Git read operations (status, diff, log, show) are allowed for understanding code context
- When the user asks for a commit, always show what will be committed first
- Always wait for user confirmation before any git write operation

## Git Worktrees for Agents

- **Agents should automatically create worktrees** to avoid disrupting user's work
- **Always create worktrees in a dedicated folder**: `../Rocket.Chat.Electron-worktrees/`
- **Branch from master unless specified otherwise** - Always create new branches from `master` branch
- When starting work on a feature/fix, agents should:
  1. Create the worktrees folder if it doesn't exist: `mkdir -p ../Rocket.Chat.Electron-worktrees`
  2. Create a new worktree branching from master: `git worktree add ../Rocket.Chat.Electron-worktrees/feature-name -b new-feature-branch master`
  3. Change to the worktree directory before making changes
  4. Install dependencies in the worktree: `yarn`
  5. Work independently without affecting the user's main repository
- **Clean up after work**: Remove worktree when task is complete or merged
- Each worktree has its own:
  - Working directory and branch
  - Build outputs (app/ folder)
  - node_modules dependencies
- Common commands:
  - `git worktree add ../Rocket.Chat.Electron-worktrees/feature-name -b new-branch` - Create worktree with new branch
  - `git worktree list` - List all worktrees
  - `git worktree remove ../Rocket.Chat.Electron-worktrees/feature-name` - Remove worktree when done