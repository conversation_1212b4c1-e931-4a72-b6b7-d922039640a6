{"private": true, "name": "@rocket.chat/desktop-release-action", "version": "1.0.0", "scripts": {"build": "ncc build src/index.ts -o dist", "lint": "run-s .:lint:eslint .:lint:tsc", ".:lint:eslint": "eslint .", ".:lint:tsc": "tsc --noEmit"}, "devDependencies": {"@rocket.chat/eslint-config": "~0.7.0", "@rocket.chat/prettier-config": "~0.31.25", "@types/node": "~16.0.3", "@typescript-eslint/eslint-plugin": "~6.17.0", "@typescript-eslint/parser": "~6.17.0", "@vercel/ncc": "~0.38.1", "eslint": "~8.56.0", "eslint-config-prettier": "~9.1.0", "eslint-import-resolver-typescript": "~3.6.1", "eslint-plugin-import": "~2.29.1", "npm-run-all": "~4.1.5", "prettier": "~3.2.5", "rimraf": "~5.0.7", "typescript": "~5.7.3"}, "dependencies": {"@actions/core": "~1.10.1", "@actions/github": "~6.0.0", "@octokit/webhooks-types": "~7.3.1", "fast-glob": "~3.3.2", "semver": "~7.5.4"}, "volta": {"node": "22.17.1", "yarn": "4.0.2"}}