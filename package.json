{"private": true, "workspaces": ["workspaces/*"], "productName": "Rocket.Chat", "name": "rocketchat", "description": "Official OSX, Windows, and Linux Desktop Clients for Rocket.Chat", "version": "4.8.1", "author": "Rocket.Chat Support <<EMAIL>>", "copyright": "© 2016-2025, Rocket.Chat", "homepage": "https://rocket.chat", "license": "MIT", "goUrlShortener": "go.rocket.chat", "keywords": ["rocketchat", "desktop", "electron"], "repository": {"type": "git", "url": "git+https://github.com/RocketChat/Rocket.Chat.Electron.git"}, "bugs": {"url": "https://github.com/RocketChat/Rocket.Chat.Electron/issues"}, "main": "app/main.js", "scripts": {"postinstall": "run-s install-app-deps clean patch-package", "patch-package": "patch-package", "start": "run-s build:watch", "clean": "rimraf app dist", "build": "rollup -c", "build:watch": "rollup -c -w", "build-mac": "yarn build && yarn electron-builder --publish never --mac --universal", "build-mas": "yarn build && yarn electron-builder --publish never --mac mas --universal", "build-win": "yarn build && yarn electron-builder --publish never --win", "build-linux": "yarn build && yarn electron-builder --publish never --linux", "build-assets": "ts-node -O '{\"module\":\"commonjs\"}' src/buildAssets.ts", "build-assets-win": "ts-node -O \"{\\\"module\\\":\\\"commonjs\\\"}\" src/buildAssets.ts", "release": "yarn electron-builder --publish onTagOrDraft --x64", "install-app-deps": "electron-builder install-app-deps", "test": "xvfb-maybe jest --forceExit --detectOpenHandles --maxWorkers=1", "changelog": "conventional-changelog -p angular -i CHANGELOG.md -s", "lint": "run-s .:lint:eslint .:lint:tsc", ".:lint:eslint": "eslint .", ".:lint:tsc": "tsc --noEmit", "lint-fix": "run-s .:lint-fix:eslint .:lint:tsc", ".:lint-fix:eslint": "eslint --fix .", "workspaces:build": "yarn workspaces foreach -At run build"}, "dependencies": {"@bugsnag/js": "~7.22.3", "@emotion/css": "~11.11.2", "@emotion/react": "~11.11.3", "@emotion/styled": "~11.11.0", "@ewsjs/xhr": "~2.0.2", "@rocket.chat/css-in-js": "~0.31.25", "@rocket.chat/fuselage": "0.58.0", "@rocket.chat/fuselage-hooks": "~0.33.1", "@rocket.chat/fuselage-polyfills": "~0.31.25", "@rocket.chat/icons": "0.37.0", "axios": "~1.6.4", "detect-browsers": "~6.1.0", "electron-dl": "4.0.0", "electron-store": "~8.1.0", "electron-updater": "^5.3.0", "ews-javascript-api": "~0.13.2", "i18next": "~23.7.16", "jsonwebtoken": "~9.0.2", "moment": "~2.30.1", "react": "~18.3.1", "react-dom": "~18.3.1", "react-hook-form": "~7.49.2", "react-i18next": "~14.0.0", "react-keyed-flatten-children": "~3.0.0", "react-redux": "~9.0.4", "react-virtuoso": "~4.6.2", "redux": "~5.0.1", "reselect": "~5.0.1", "rimraf": "~5.0.7", "semver": "~7.5.4"}, "devDependencies": {"@babel/core": "~7.23.9", "@babel/eslint-parser": "~7.23.3", "@babel/plugin-proposal-class-properties": "~7.18.6", "@babel/plugin-proposal-function-bind": "~7.23.3", "@babel/preset-env": "~7.23.7", "@babel/preset-react": "~7.23.3", "@babel/preset-typescript": "~7.23.3", "@electron/fuses": "~1.8.0", "@fiahfy/icns-convert": "~0.0.12", "@fiahfy/ico-convert": "~0.0.12", "@kayahr/jest-electron-runner": "29.14.0", "@rocket.chat/eslint-config": "~0.7.0", "@rocket.chat/prettier-config": "~0.31.25", "@rollup/plugin-babel": "~6.0.4", "@rollup/plugin-commonjs": "~25.0.7", "@rollup/plugin-json": "~6.1.0", "@rollup/plugin-node-resolve": "~15.2.3", "@rollup/plugin-replace": "~5.0.5", "@types/electron-devtools-installer": "~2.2.5", "@types/jest": "~29.5.11", "@types/jsonwebtoken": "~9.0.5", "@types/node": "~16.18.69", "@types/react": "~18.3.18", "@types/react-dom": "~18.3.5", "@typescript-eslint/eslint-plugin": "~6.17.0", "@typescript-eslint/parser": "~6.17.0", "builtin-modules": "~3.3.0", "chokidar": "~3.5.3", "conventional-changelog-cli": "~4.1.0", "convert-svg-to-png": "~0.6.4", "electron": "37.2.4", "electron-builder": "26.0.3", "electron-devtools-installer": "^3.2.0", "electron-notarize": "^1.2.2", "eslint": "~8.56.0", "eslint-config-prettier": "~9.1.0", "eslint-import-resolver-typescript": "~3.6.1", "eslint-plugin-import": "~2.26.0", "eslint-plugin-prettier": "~5.1.2", "eslint-plugin-react": "~7.33.2", "eslint-plugin-react-hooks": "~4.6.0", "jest": "~29.7.0", "jest-environment-jsdom": "~29.7.0", "jimp": "~0.22.10", "npm-run-all": "~4.1.5", "patch-package": "~8.0.0", "prettier": "~3.2.5", "puppeteer": "23.1.1", "rollup": "~4.9.6", "rollup-plugin-copy": "~3.5.0", "ts-jest": "~29.1.4", "ts-node": "~10.9.2", "typescript": "~5.7.3", "xvfb-maybe": "~0.2.1"}, "optionalDependencies": {"fsevents": "2.3.3"}, "engines": {"node": ">=22.17.1"}, "devEngines": {"node": ">=22.17.1", "yarn": ">=4.0.2"}, "resolutions": {"@fiahfy/icns-convert/sharp": "0.29.3", "@fiahfy/ico-convert/sharp": "0.29.3"}, "volta": {"node": "22.17.1", "yarn": "4.0.2"}, "packageManager": "yarn@4.6.0"}